import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, BackHandler, FlatList } from 'react-native';
import { Text, IconButton, Button, useTheme, Appbar, Card, Chip } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useQuizResult } from './store/QuizResultContext';
import { useUserProgress } from './store/UserProgressContext';
import { useExamContext } from './store/ExamContext';
import AdmobService from './services/AdmobService';
import { usePurchase } from './store/PurchaseContext';
import { useSubscriptionRefresh } from './utils/subscriptionUtils';
import FixedFontButton from './components/FixedFontButton';
import CustomAppbarContent from './components/CustomAppbarContent';

// Helper functions for time formatting
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
};

const formatTimeShort = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  if (minutes > 0) {
    return `${minutes}m`;
  }
  return `${seconds}s`;
};

const QuizResult = ({ route, navigation }) => {
  const { subscriptionActive } = usePurchase();

  useSubscriptionRefresh();

  const { colors } = useTheme();
  const { examId } = route.params;
  const [filter, setFilter] = useState('all');
  const [bookmarkedIds, setBookmarkedIds] = useState(new Set());
  const [isTabBarSticky, setIsTabBarSticky] = useState(false);
  const [tabBarHeight, setTabBarHeight] = useState(0);
  const [statsContainerY, setStatsContainerY] = useState(0);
  const [tabBarY, setTabBarY] = useState(0);

  useEffect(() => {
    console.log('tabBarY updated:', tabBarY);
  }, [tabBarY]);

  const flatListRef = React.useRef(null);

  const { resultId, resultData } = route.params || {};
  const { results } = useQuizResult();
  const examResults = results.filter(r => r.examId === examId);
  const currentResult = resultData || examResults.find(r => r.id === resultId);

  const total = currentResult?.totalQuestions || 0;
  const correct = currentResult?.correctAnswers || 0;
  const time = currentResult?.timeSpent || 0;
  const percentage = total > 0 ? (correct / total) * 100 : 0;
  const incorrectCount = total - correct;

  const questionsWithIndex = currentResult?.questions?.map(q => ({
    ...q,
    originalIndex: q.originalIndex,
  })) || [];

  // Convert flaggedIds to an array if it's not already
  const flaggedIds = Array.isArray(currentResult?.flaggedIds)
    ? currentResult.flaggedIds
    : (currentResult?.flaggedIds ? Array.from(currentResult.flaggedIds) : []);
  const userAnswers = currentResult?.answers || {};

  const filteredQuestions = useMemo(() =>
    questionsWithIndex.filter(q => {
      const userAnswer = userAnswers[q.originalIndex] || [];
      const isCorrect = userAnswer.length === q.answer.length &&
        q.answer.every(a => userAnswer.includes(a));

      switch (filter) {
        case 'incorrect': return !isCorrect;
        case 'correct': return isCorrect;
        case 'flagged':
          // Check if the question's originalIndex is in the flaggedIds array
          return flaggedIds.some(id =>
            // Handle both number and string comparisons
            id === q.originalIndex ||
            id === q.id ||
            (typeof id === 'string' && id === String(q.originalIndex)) ||
            (typeof id === 'number' && id === Number(q.originalIndex))
          );
        case 'bookmarked': return bookmarkedIds.has(q.originalIndex);
        default: return true;
      }
    }),
    [questionsWithIndex, filter, flaggedIds, bookmarkedIds, userAnswers]
  );

  const tabs = useMemo(() => [
    { value: 'all', label: 'All', count: total },
    { value: 'flagged', label: 'Flagged', count: flaggedIds.length },
    { value: 'incorrect', label: 'Incorrect', count: incorrectCount },
    { value: 'correct', label: 'Correct', count: correct },
  ], [total, flaggedIds.length, incorrectCount, correct]);

  // Handle closing quiz result
  const handleClose = useCallback(() => {
    if (!subscriptionActive) {
      AdmobService.showInterstitialAd(
        () => {
          console.log('Interstitial ad loaded');
        },
        () => {
          navigation.reset({ index: 0, routes: [{ name: 'Quiz' }] });
        }
      );
    } else {
      // If subscription is active, navigate immediately without showing ad
      navigation.reset({ index: 0, routes: [{ name: 'Quiz' }] });
    }
  }, [navigation, subscriptionActive]);

  // Handle filter change and scroll to the first item in the filtered list
  const handleFilterChange = useCallback((newFilter) => {
    setFilter(newFilter);

    // Use setTimeout to ensure the filtered list is updated before scrolling
    setTimeout(() => {
      console.log('Scrolling to top of filtered list:', tabBarY);
      if (flatListRef.current) {
        flatListRef.current.scrollToOffset({
          offset: tabBarY,
          animated: true
        });
      }
    }, 100);
  }, [tabBarY]);

  const toggleBookmark = useCallback((id) => {
    setBookmarkedIds(prev => {
      const newSet = new Set(prev);
      newSet.has(id) ? newSet.delete(id) : newSet.add(id);
      return newSet;
    });
  }, []);

  const MemoizedQuestionCard = React.memo(({ item, userAnswers, colors, navigation, filter, flaggedIds, filteredQuestions }) => {
    const { toggleBookmark, progress } = useUserProgress();
    const { selectedExam } = useExamContext();

    const isBookmarked = Boolean(
      selectedExam?.id &&
      item?.subject &&
      item?.id &&
      progress[selectedExam.id]?.[item.subject]?.bookmarked?.some(b => b.id === item.id)
    );

    // Update bookmark toggle
    const handleBookmarkToggle = useCallback(() => {
      if (!selectedExam?.id || !item?.subject || !item?.id) return;
      toggleBookmark(selectedExam.id, item.subject, item.id);
    }, [selectedExam, item, toggleBookmark]);

    const userAnswer = userAnswers[item.originalIndex] || [];
    const isCorrect = item.answer.length === userAnswer.length &&
      item.answer.every(a => userAnswer.includes(a));

    const handlePress = useCallback(() => {
      navigation.navigate('QuestionStack', {
        screen: 'QnADetail',
        params: {
          filteredData: filteredQuestions,
          initialIndex: filteredQuestions.findIndex(q => q.originalIndex === item.originalIndex),
          activeTabLabel: filter,
          userAnswers,
          fromQuizResult: true,
          originalIndexes: filteredQuestions.reduce((acc, q) => ({
            ...acc,
            [q.originalIndex]: true
          }), {})
        }
      });
    }, [filter, filteredQuestions, item.originalIndex]);

    return (
      <Card style={[styles.questionCard, { backgroundColor: colors.surface }]} onPress={handlePress}>
        <Card.Content style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <Text variant="titleSmall" style={styles.questionNumber}>
              Question #{item.originalIndex + 1}
            </Text>
            <IconButton
              icon={isBookmarked ? 'bookmark' : 'bookmark-outline'}
              onPress={handleBookmarkToggle}
              iconColor={colors.primary}
              size={20}
              style={styles.bookmarkIcon}
            />
          </View>

          <Text variant="bodyMedium" style={[styles.questionText, { color: colors.onSurface }]}>
            {item.first_sentence}
          </Text>

          <View style={styles.answerContainer}>
            <MemoizedAnswerChip
              isCorrect={isCorrect}
              userAnswer={userAnswer}
              colors={colors}
              textStyle={{
                color: isCorrect ? colors.onSecondaryContainer :
                  userAnswer.length === 0 ? colors.onSurfaceVariant :
                    colors.onErrorContainer,
                fontWeight: '500'  // Moved from styles.chipText
              }}
            />

            <MemoizedCorrectAnswerChip
              answer={item.answer}
              colors={colors}
              textStyle={{
                color: colors.onSecondaryContainer,
                fontWeight: '500'
              }}
            />
          </View>

          {flaggedIds.some(id =>
            id === item.originalIndex ||
            id === item.id ||
            (typeof id === 'string' && id === String(item.originalIndex)) ||
            (typeof id === 'number' && id === Number(item.originalIndex))
          ) && (
              <MemoizedFlagChip colors={colors}
                textStyle={{
                  color: colors.error,
                  fontWeight: '500'
                }} />
            )}
        </Card.Content>
      </Card>
    );
  }, (prevProps, nextProps) => {
    // Only re-render if these specific props change
    return prevProps.item.originalIndex === nextProps.item.originalIndex &&
      prevProps.userAnswers[prevProps.item.originalIndex] === nextProps.userAnswers[nextProps.item.originalIndex] &&
      prevProps.bookmarkedIds.has(prevProps.item.originalIndex) === nextProps.bookmarkedIds.has(nextProps.item.originalIndex) &&
      prevProps.filter === nextProps.filter;
  });

  const MemoizedAnswerChip = React.memo(({ isCorrect, userAnswer, colors }) => (
    <Chip
      mode="outlined"
      icon={isCorrect ? 'check' : userAnswer.length === 0 ? 'alert-circle-outline' : 'close'}
      textStyle={{
        color: isCorrect ? colors.onSecondaryContainer :
          userAnswer.length === 0 ? colors.onSurfaceVariant :
            colors.onErrorContainer
      }}
      style={[
        styles.answerChip,
        {
          borderColor: isCorrect ? colors.secondaryContainer :
            userAnswer.length === 0 ? colors.outline :
              colors.errorContainer
        }
      ]}
    >
      Your Answer: {userAnswer.length > 0 ? userAnswer.join(', ') : 'Unanswered'}
    </Chip>
  ));

  const MemoizedCorrectAnswerChip = React.memo(({ answer, colors }) => (
    <Chip
      mode="outlined"
      icon="check-all"
      textStyle={{ color: colors.onSecondaryContainer }}
      style={[styles.answerChip, { borderColor: colors.secondaryContainer }]}
    >
      Correct: {(answer || []).join(', ')}
    </Chip>
  ));

  const MemoizedFlagChip = React.memo(({ colors }) => (
    <Chip
      icon="flag"
      mode="outlined"
      style={[styles.flagChip, { borderColor: colors.error }]}
      textStyle={{ color: colors.error }}
    >
      Flagged
    </Chip>
  ));

  const TabBar = React.memo(({ tabs, filter, setFilter, colors, onLayout, isSticky = false, handleFilterChange }) => (
    <View
      style={[
        styles.tabBarContainer,
        { backgroundColor: colors.surfaceVariant },
        isSticky && styles.stickyHeader
      ]}
      onLayout={onLayout}
    >
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.value}
          onPress={() => handleFilterChange ? handleFilterChange(tab.value) : setFilter(tab.value)}
          style={[
            styles.tabButton,
            {
              borderBottomWidth: filter === tab.value ? 3 : 0,
              borderBottomColor: colors.primary,
            }
          ]}>
          <Text style={[
            styles.tabLabel,
            { color: filter === tab.value ? colors.primary : colors.onSurface }
          ]} maxFontSizeMultiplier={1.2}>
            {tab.label}
          </Text>
          <Text style={[
            styles.tabCount,
            { color: filter === tab.value ? colors.primary : colors.onSurface }
          ]} maxFontSizeMultiplier={1.2}>
            {tab.count}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  ));

  // Handle hardware back button - now uses handleClose for consistent behavior
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      handleClose();
      return true; // Prevent default back behavior
    });
    return () => backHandler.remove();
  }, [handleClose]);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Appbar.Header elevated>
        <Appbar.Action
          icon="close"
          onPress={handleClose}
          color={colors.onSurface}
        />
        <CustomAppbarContent title="Results Summary" titleStyle={styles.headerTitle} />
      </Appbar.Header>

      {/* Sticky Tab Bar that appears when scrolling */}
      {isTabBarSticky && (
        <View style={[styles.stickyTabBarContainer, { backgroundColor: colors.surfaceVariant }]}>
          <TabBar
            tabs={tabs}
            filter={filter}
            setFilter={setFilter}
            colors={colors}
            isSticky={true}
            handleFilterChange={handleFilterChange}
          />
        </View>
      )}

      <FlatList
        ref={flatListRef}
        data={filteredQuestions}
        keyExtractor={(item) => item.originalIndex.toString()}
        initialNumToRender={10}
        windowSize={21}
        maxToRenderPerBatch={10}
        removeClippedSubviews={true}
        onScroll={(event) => {
          const scrollY = event.nativeEvent.contentOffset.y;
          // The position where the tab bar should become sticky
          // This is the position where the original tab bar would be in the scroll view
          const tabBarPosition = 450; // Adjusted based on the layout (stats card + retake button height)
          setIsTabBarSticky(scrollY > tabBarPosition);
        }}
        scrollEventThrottle={16} // For smooth scroll event handling
        contentContainerStyle={{
          paddingTop: isTabBarSticky ? tabBarHeight : 0
        }}
        ListHeaderComponent={
          <>
            <View style={styles.statsContainer}
              onLayout={(event) => {
                console.log('Stats container layout updated:', event.nativeEvent.layout);
                setStatsContainerY(event.nativeEvent.layout.y)
              }}>
              <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
                <Card.Content style={styles.statContent}>
                  {/* Score Section */}
                  <View style={styles.scoreSection}>
                    <View style={styles.scoreCircle}>
                      <Text variant="headlineMedium" style={[styles.percentage, { color: colors.primary }]}>
                        {percentage.toFixed(0)}%
                      </Text>
                      <Text variant="bodySmall" style={{ color: colors.onSurfaceVariant, textAlign: 'center' }}>
                        Score
                      </Text>
                    </View>

                    <View style={styles.scoreDetails}>
                      <Text variant="titleMedium" style={{ color: colors.onSurface, marginBottom: 8, fontWeight: '600' }}>
                        Quiz Performance
                      </Text>
                      <Text variant="bodyMedium" style={{ color: colors.onSurfaceVariant, marginBottom: 4 }} maxFontSizeMultiplier={1.2}>
                        You answered {correct} out of {total} questions correctly.
                      </Text>
                      <Text variant="bodyMedium" style={{ color: colors.onSurfaceVariant }} maxFontSizeMultiplier={1.2}>
                        Time taken: {formatTime(time)}
                      </Text>
                    </View>
                  </View>

                  {/* Progress Bar Section */}
                  {/* <View style={styles.progressSection}>
                    <View style={styles.progressBar}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            width: `${percentage}%`,
                            backgroundColor: percentage >= 70 ? colors.success : colors.error
                          }
                        ]}
                      />
                    </View>
                    <View style={styles.progressLabels}>
                      <Text variant="bodySmall" style={{ color: colors.onSurfaceVariant }}>0%</Text>
                      <Text variant="bodySmall" style={{ color: colors.onSurfaceVariant }}>100%</Text>
                    </View>
                  </View> */}

                  {/* Stats Grid */}
                  <View style={styles.statsGrid}>
                    <View style={[styles.statItem, styles.statItemEnhanced, { backgroundColor: colors.successContainer || 'rgba(46, 125, 50, 0.1)' }]}>
                      <MaterialCommunityIcons name="check-circle" size={24} color={colors.onSurface} />
                      <View style={styles.statValueContainer}>
                        <Text variant="titleLarge" style={[styles.statValue, { color: colors.onSurface }]}>
                          {correct}
                        </Text>
                      </View>
                      <Text variant="bodySmall" style={{ color: colors.onSurface }}>
                        Correct
                      </Text>
                    </View>

                    <View style={[styles.statItem, styles.statItemEnhanced, { backgroundColor: colors.errorContainer || 'rgba(211, 47, 47, 0.1)' }]}>
                      <MaterialCommunityIcons name="close-circle" size={24} color={colors.error} />
                      <View style={styles.statValueContainer}>
                        <Text variant="titleLarge" style={[styles.statValue, { color: colors.onSurface }]}>
                          {incorrectCount}
                        </Text>
                      </View>
                      <Text variant="bodySmall" style={{ color: colors.onSurface }}>
                        Incorrect
                      </Text>
                    </View>

                    <View style={[styles.statItem, styles.statItemEnhanced, { backgroundColor: colors.primaryContainer || 'rgba(33, 150, 243, 0.1)' }]}>
                      <MaterialCommunityIcons name="clock-outline" size={24} color={colors.primary} />
                      <View style={styles.statValueContainer}>
                        <Text variant="titleLarge" style={[styles.statValue, { color: colors.onSurface }]}>
                          {formatTimeShort(time)}
                        </Text>
                      </View>
                      <Text variant="bodySmall" style={{ color: colors.onSurface }}>
                        Time
                      </Text>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            </View>

            <FixedFontButton
              mode="contained"
              onPress={() => {
                navigation.replace('QuizMode', {
                  retakeQuestions: currentResult.questions
                });
              }}
              style={styles.retakeButton}
              labelStyle={{ color: '#FFFFFF', fontWeight: '600', fontSize: 16 }}
              icon="reload"
              theme={{ colors: { primary: colors.primary } }}
            >
              Retake Quiz
            </FixedFontButton>

            {/* Tab Bar Placeholder - only used to measure height */}
            <View
              style={{ opacity: 0, height: 0, overflow: 'hidden' }}
              onLayout={(event) => {
                setTabBarY(event.nativeEvent.layout.y);
                console.log('Tab Bar Placeholder layout updated: ', event.nativeEvent.layout);
                console.log('Tab Bar Placeholder layout updated, y: ', event.nativeEvent.layout.y);
                console.log('tabBarY: ', tabBarY);
                // Store the height of the tab bar for proper spacing when it becomes sticky
                if (!tabBarHeight) {
                  setTabBarHeight(event.nativeEvent.layout.height);
                }
              }}
            >
              <TabBar
                tabs={tabs}
                filter={filter}
                setFilter={setFilter}
                colors={colors}
                handleFilterChange={handleFilterChange}
              />
            </View>

            {/* Visible Tab Bar */}
            <View style={[styles.tabBarWrapper, { backgroundColor: colors.surfaceVariant }]}>
              <TabBar
                tabs={tabs}
                filter={filter}
                setFilter={setFilter}
                colors={colors}
                handleFilterChange={handleFilterChange}
              />
            </View>
          </>
        }
        ListEmptyComponent={
          <Text style={[styles.emptyText, { color: colors.onSurfaceVariant }]}>
            No questions found in this category.
          </Text>
        }
        renderItem={({ item, index }) => (
          <MemoizedQuestionCard
            item={item}
            index={index}
            userAnswers={userAnswers}
            colors={colors}
            navigation={navigation}
            filter={filter}
            flaggedIds={flaggedIds}
            bookmarkedIds={bookmarkedIds}
            toggleBookmark={toggleBookmark}
            filteredQuestions={filteredQuestions}
          />
        )}
        ListFooterComponent={<View style={{ height: 24 }} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },
  headerTitle: {
    fontWeight: '600',
    fontSize: 18,
  },
  statsContainer: {
    paddingHorizontal: 16,
    marginTop: 8,
  },
  statCard: {
    borderRadius: 16,
    elevation: 2,
  },
  statContent: {
    padding: 20,
    gap: 20,
  },
  scoreSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  scoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  scoreDetails: {
    flex: 1,
  },
  percentage: {
    fontWeight: '800',
  },
  progressSection: {
    marginVertical: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  statItem: {
    alignItems: 'center',
    gap: 4,
  },
  statItemEnhanced: {
    padding: 12,
    borderRadius: 12,
    minWidth: 80,
  },
  statValueContainer: {
    marginVertical: 4,
  },
  statValue: {
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  retakeButton: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
    borderRadius: 8,
    paddingVertical: 6,
    width: '90%',
    alignSelf: 'center',
  },
  buttonLabel: {
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  tabsContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  tabBarWrapper: {
    marginBottom: 8,
  },
  tabBarContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    padding: 10,
    paddingBottom: 0,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tabButton: {
    paddingBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  tabLabel: {
    fontWeight: '500',
    fontSize: 13,
  },
  tabCount: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  stickyHeader: {
    position: 'absolute',
    left: 0,
    right: 0,
    top:0,
    zIndex: 100,
  },
  stickyTabBarContainer: {
    position: 'absolute',
    top: 64, // Height of the app bar
    left: 0,
    right: 0,
    zIndex: 100,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  questionCard: {
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
  },
  cardContent: {
    paddingVertical: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  questionNumber: {
    fontWeight: '600',
    opacity: 0.8,
  },
  bookmarkIcon: {
    margin: 0,
  },
  questionText: {
    marginBottom: 16,
    lineHeight: 20,
  },
  answerContainer: {
    gap: 8,
    marginBottom: 12,
  },
  answerChip: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
  },
  flagChip: {
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 24,
    fontSize: 16,
  }
});

export default QuizResult;