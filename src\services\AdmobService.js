import { Platform, View, StyleSheet } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import {
  BannerAd,
  BannerAdSize,
  TestIds,
  InterstitialAd,
  AdEventType,
  RewardedAd,
  RewardedAdEventType,
} from 'react-native-google-mobile-ads';
import {
  ADMOB_ANDROID_BANNER_ID,
  ADMOB_ANDROID_INTERSTITIAL_ID,
  ADMOB_ANDROID_REWARDED_ID,
  ADMOB_IOS_BANNER_ID,
  ADMOB_IOS_INTERSTITIAL_ID,
  ADMOB_IOS_REWARDED_ID
} from '@env';

class AdmobService {
  constructor() {
    this.interstitialAd = null;
    this.rewardedAd = null;
    this.isEmulator = false;
    this.isInitialized = false;
    this.adUnitIds = null;
    
    // Initialize emulator detection and ads
    this.initializeService();
  }

  async initializeService() {
    try {
      // Detect if running on emulator
      this.isEmulator = await DeviceInfo.isEmulator();
      
      console.log('[AdmobService] Initialization:', {
        platform: Platform.OS,
        isDev: __DEV__,
        isEmulator: this.isEmulator,
        useTestIds: __DEV__ || this.isEmulator
      });

      // Set up ad unit IDs based on environment and device type
      this.adUnitIds = {
        banner: Platform.select({
          ios: (__DEV__ || this.isEmulator) ? TestIds.BANNER : ADMOB_IOS_BANNER_ID,
          android: (__DEV__ || this.isEmulator) ? TestIds.BANNER : ADMOB_ANDROID_BANNER_ID,
        }),
        interstitial: Platform.select({
          ios: (__DEV__ || this.isEmulator) ? TestIds.INTERSTITIAL : ADMOB_IOS_INTERSTITIAL_ID,
          android: (__DEV__ || this.isEmulator) ? TestIds.INTERSTITIAL : ADMOB_ANDROID_INTERSTITIAL_ID,
        }),
        rewarded: Platform.select({
          ios: (__DEV__ || this.isEmulator) ? TestIds.REWARDED : ADMOB_IOS_REWARDED_ID,
          android: (__DEV__ || this.isEmulator) ? TestIds.REWARDED : ADMOB_ANDROID_REWARDED_ID,
        }),
      };

      console.log('[AdmobService] Ad Unit IDs configured:', {
        banner: this.adUnitIds.banner,
        interstitial: this.adUnitIds.interstitial,
        rewarded: this.adUnitIds.rewarded,
        usingTestIds: __DEV__ || this.isEmulator
      });

      // Initialize ads after configuration is ready
      this.initAds();
      this.isInitialized = true;
      
    } catch (error) {
      console.error('[AdmobService] Initialization failed:', error);
      
      // Fallback to test IDs if emulator detection fails
      this.isEmulator = true;
      this.adUnitIds = {
        banner: TestIds.BANNER,
        interstitial: TestIds.INTERSTITIAL,
        rewarded: TestIds.REWARDED,
      };
      
      console.log('[AdmobService] Using fallback test IDs due to initialization error');
      this.initAds();
      this.isInitialized = true;
    }
  }

  // Wait for initialization to complete
  async waitForInitialization() {
    if (this.isInitialized) return;
    
    // Poll until initialized (with timeout)
    const maxWait = 5000; // 5 seconds
    const pollInterval = 100; // 100ms
    let waited = 0;
    
    while (!this.isInitialized && waited < maxWait) {
      await new Promise(resolve => setTimeout(resolve, pollInterval));
      waited += pollInterval;
    }
    
    if (!this.isInitialized) {
      console.warn('[AdmobService] Initialization timeout, using fallback configuration');
      this.adUnitIds = {
        banner: TestIds.BANNER,
        interstitial: TestIds.INTERSTITIAL,
        rewarded: TestIds.REWARDED,
      };
      this.initAds();
      this.isInitialized = true;
    }
  }

  initAds() {
    if (!this.adUnitIds) {
      console.warn('[AdmobService] Ad unit IDs not ready, skipping ad initialization');
      return;
    }

    try {
      // Initialize interstitial ad
      this.interstitialAd = InterstitialAd.createForAdRequest(this.adUnitIds.interstitial, {
        requestNonPersonalizedAdsOnly: true,
      });

      // Initialize rewarded ad
      this.rewardedAd = RewardedAd.createForAdRequest(this.adUnitIds.rewarded, {
        requestNonPersonalizedAdsOnly: true,
      });

      console.log('[AdmobService] Ads initialized successfully');
    } catch (error) {
      console.error('[AdmobService] Failed to initialize ads:', error);
    }
  }

  // Get ad unit IDs (with fallback for immediate access)
  getAdUnitIds() {
    if (this.adUnitIds) {
      return this.adUnitIds;
    }
    
    // Fallback for immediate access before initialization
    console.warn('[AdmobService] Using fallback ad unit IDs before initialization');
    return {
      banner: TestIds.BANNER,
      interstitial: TestIds.INTERSTITIAL,
      rewarded: TestIds.REWARDED,
    };
  }

  // Banner Ad Component
  renderBannerAd(size = BannerAdSize.BANNER, onAdFailedToLoad) {
    const adUnitIds = this.getAdUnitIds();
    
    return (
      <BannerAd
        unitId={adUnitIds.banner}
        size={size}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
        onAdFailedToLoad={onAdFailedToLoad}
      />
    );
  }

  // Reusable Banner Ad Container with default styling
  renderBannerAdContainer(size = BannerAdSize.BANNER, onAdFailedToLoad) {
    const styles = this.constructor.getStyles();
    return (
      <View style={styles.bannerContainer}>
        {this.renderBannerAd(size, (error) => {
          console.log('Banner ad failed to load:', error);
          onAdFailedToLoad?.(error);
        })}
      </View>
    );
  }

  // Styles for banner container
  static getStyles() {
    return StyleSheet.create({
      bannerContainer: {
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'transparent',
        paddingHorizontal: 16,
      },
    });
  }

  // Show Interstitial Ad
  async showInterstitialAd(onAdLoaded, onAdClosed) {
    await this.waitForInitialization();
    
    if (!this.interstitialAd) {
      console.error('[AdmobService] Interstitial ad not initialized');
      onAdClosed?.();
      return;
    }

    this.interstitialAd.load();

    const unsubscribeLoaded = this.interstitialAd.addAdEventListener(
      AdEventType.LOADED,
      () => {
        onAdLoaded?.();
        this.interstitialAd.show();
        unsubscribeLoaded();
      }
    );

    const unsubscribeClosed = this.interstitialAd.addAdEventListener(
      AdEventType.CLOSED,
      () => {
        onAdClosed?.();
        this.initAds(); // Reload ad for next use
        unsubscribeClosed();
      }
    );
  }

  // Show Rewarded Ad
  async showRewardedAd(onRewardEarned, onAdClosed, onError) {
    await this.waitForInitialization();
    
    if (!this.rewardedAd) {
      console.error('[AdmobService] Rewarded ad not initialized');
      onError?.('Rewarded ad not initialized');
      return;
    }

    this.rewardedAd.load();

    const unsubscribeLoaded = this.rewardedAd.addAdEventListener(
      RewardedAdEventType.LOADED,
      () => {
        this.rewardedAd.show();
        unsubscribeLoaded();
      }
    );

    const unsubscribeFailed = this.rewardedAd.addAdEventListener(
      AdEventType.ERROR,
      (error) => {
        onError?.(error);
        unsubscribeFailed();
        unsubscribeLoaded();
        unsubscribeEarned();
        unsubscribeClosed();
      }
    );

    const unsubscribeEarned = this.rewardedAd.addAdEventListener(
      RewardedAdEventType.EARNED_REWARD,
      (reward) => {
        onRewardEarned?.(reward);
        unsubscribeEarned();
      }
    );

    const unsubscribeClosed = this.rewardedAd.addAdEventListener(
      AdEventType.CLOSED,
      () => {
        onAdClosed?.();
        this.initAds(); // Reload ad for next use
        unsubscribeClosed();
      }
    );
  }

  // Utility method to check if using test ads
  isUsingTestAds() {
    return __DEV__ || this.isEmulator;
  }

  // Get current configuration info
  getConfigInfo() {
    return {
      isInitialized: this.isInitialized,
      isEmulator: this.isEmulator,
      isDev: __DEV__,
      usingTestIds: this.isUsingTestAds(),
      adUnitIds: this.adUnitIds
    };
  }
}

// Export as singleton instance
export default new AdmobService();