import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { View, FlatList, TouchableOpacity, Animated, StyleSheet, Alert, Dimensions } from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import AdmobService from '../services/AdmobService';
import { BannerAdSize } from 'react-native-google-mobile-ads';
import {
  Text, useTheme,
} from 'react-native-paper';
import AIChatHeader from './AIChatHeader';
import ChatBox from './ChatBox';
import RenderMessage from './RenderItem';
import apiClient from '../services/ApiClient';
import { useRoute } from '@react-navigation/native';
import { useAIChat } from '../store/AIChatContext';
import { useQnAContext } from '../store/QnAContext';
import { useAICredit } from '../store/AICreditContext';
import { useLogin } from '../store/LoginContext';
import LoginModal from '../components/LoginModal';
import AICreditModal from '../components/AICreditModal';
import { usePurchase } from '../store/PurchaseContext';
import { useExamContext } from '../store/ExamContext';
import StickyBottomAdMob from '../components/StickyBottomAdMob';
import { useSubscriptionRefresh } from '../utils/subscriptionUtils';

// Add safety check for context
const useSafeAIChat = () => {
  const context = useAIChat();
  if (!context) {
    console.error('AIChatContext not found. Make sure AIChatProvider is wrapping your app.');
    return {
      getChatHistory: () => ({
        messages: [
          {
            id: 'welcome-message',
            choices: [{
              index: 0,
              message: {
                role: 'assistant',
                content: `I'm here to help with this Q&A question. What would you like to know?`,
                isLoading: false
              }
            }]
          }
        ],
        quickReplies: [
          'Explain why the answer is correct and others are wrong.',
          "Give an example to clarify the answer.",
          "Share references for the correct answer.",
        ]
      }),
      /* updateChatHistory: () => { } */
    };
  }
  return context;
};

const ChatScreen = () => {
  const theme = useTheme();
  const route = useRoute();
  const { examCode, qnaId } = route.params || {};
  const { getChatHistory, updateChatHistory } = useSafeAIChat();
  const { qnas } = useQnAContext();
  const { credits, useCredits } = useAICredit();
  const { isLoggedIn } = useLogin() || {};
  const { subscriptionActive, getSubscriptionsInfo } = usePurchase();
  const { selectedExam } = useExamContext();

  useSubscriptionRefresh();
  
  // State for login modal
  const [loginModalVisible, setLoginModalVisible] = useState(false);

  // Find the current question by qnaId
  const currentQuestion = qnas.find(q => q.id === qnaId);

  // Load saved chat history if available
  const initialChatState = useMemo(() => {
    const savedHistory = getChatHistory(examCode, qnaId);

    // If there's no saved history or it's empty, use default values
    if (!savedHistory || !savedHistory.messages || savedHistory.messages.length === 0) {
      return {
        messages: [
          {
            id: 'welcome-message',
            choices: [{
              index: 0,
              message: {
                role: 'assistant',
                content: `I'm here to help with this Q&A question. What would you like to know?`,
                isLoading: false
              }
            }]
          }
        ],
        quickReplies: [
          'Explain why the answer is correct and others are wrong.',
          "Give an example to clarify the answer.",
          "Share references for the correct answer.",
        ]
      };
    }

    return savedHistory;
  }, [examCode, qnaId, getChatHistory]);

  const [messages, setMessages] = useState(initialChatState.messages);
  const [inputMessage, setInputMessage] = useState('');
  const [quickReplies, setQuickReplies] = useState(initialChatState.quickReplies);
  const [adError, setAdError] = useState(null);
  const [loading, setLoading] = useState(false);
  const flatListRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const [creditModalVisible, setCreditModalVisible] = useState(false);
  const requestTimeoutRef = useRef(null);
  const abortControllerRef = useRef(null);
  const isInitialRender = useRef(true);
  const hasCompletedInitialLoad = useRef(false);

  // State for scroll-to-bottom functionality
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const scrollToBottomOpacity = useRef(new Animated.Value(0)).current;

  // Enhanced refs for better scroll behavior and measurements
  const scrollTimeoutRef = useRef(null);
  const lastScrollTimeRef = useRef(0);
  const flatListLayoutRef = useRef({ height: 0, width: 0 });
  const contentSizeRef = useRef({ height: 0, width: 0 });
  const currentScrollOffsetRef = useRef(0);
  const scrollToBottomAttemptRef = useRef(0);

  // Ref to track current loading placeholder message ID
  const currentLoadingMessageIdRef = useRef(null);

  // Function to cleanup loading state when user enters page
  const cleanupLoadingStateOnEntry = useCallback(() => {
    console.log('Cleaning up any existing loading state on page entry');
    
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // Clear any pending timeouts
    if (requestTimeoutRef.current) {
      clearTimeout(requestTimeoutRef.current);
      requestTimeoutRef.current = null;
    }

    // Remove any loading placeholder messages AND their associated user messages
    setMessages(prev => {
      const cleanedMessages = [];
      let removedCount = 0;
      
      for (let i = 0; i < prev.length; i++) {
        const currentMsg = prev[i];
        const isLoadingMessage = currentMsg.choices[0].message.isLoading;
        
        if (isLoadingMessage) {
          // Found a loading message, also remove the previous user message if it exists
          if (cleanedMessages.length > 0) {
            const previousMsg = cleanedMessages[cleanedMessages.length - 1];
            if (previousMsg.choices[0].message.role === 'user') {
              // Remove the previous user message
              cleanedMessages.pop();
              removedCount++;
              console.log('Removed user message that triggered loading:', previousMsg.choices[0].message.content.substring(0, 50) + '...');
            }
          }
          // Don't add the loading message
          removedCount++;
          console.log('Removed loading message');
        } else {
          // Keep non-loading messages
          cleanedMessages.push(currentMsg);
        }
      }
      
      if (removedCount > 0) {
        console.log('Removed', removedCount, 'messages on page entry (loading messages and their associated user questions)');
      }
      
      return cleanedMessages;
    });

    // Clear the loading message ref
    currentLoadingMessageIdRef.current = null;

    // Reset loading state
    setLoading(false);
  }, []);

  // Enhanced function to scroll to bottom with multiple fallback methods
  const scrollToBottom = useCallback(() => {
    if (!flatListRef.current) return;

    const attemptScroll = (attempt = 0) => {
      if (attempt > 3) {
        console.warn('Failed to scroll to bottom after multiple attempts');
        return;
      }

      const { height: layoutHeight } = flatListLayoutRef.current;
      const { height: contentHeight } = contentSizeRef.current;

      console.log('Scroll attempt', attempt + 1, {
        layoutHeight,
        contentHeight,
        shouldScroll: contentHeight > layoutHeight
      });

      if (contentHeight > layoutHeight) {
        // Method 1: Use scrollToEnd (most reliable for FlatList)
        if (attempt === 0) {
          flatListRef.current.scrollToEnd({ animated: true });
        }
        // Method 2: Use scrollToOffset with calculated position
        else if (attempt === 1) {
          const targetOffset = Math.max(0, contentHeight - layoutHeight);
          flatListRef.current.scrollToOffset({ 
            offset: targetOffset, 
            animated: true 
          });
        }
        // Method 3: Scroll to last item index
        else if (attempt === 2 && messages.length > 0) {
          flatListRef.current.scrollToIndex({ 
            index: messages.length - 1, 
            animated: true,
            viewPosition: 1 // Scroll to bottom of the item
          });
        }
        // Method 4: Force scroll with larger offset
        else {
          const targetOffset = contentHeight + 100; // Add extra padding
          flatListRef.current.scrollToOffset({ 
            offset: targetOffset, 
            animated: true 
          });
        }

        // Update the last scroll time to track programmatic scrolls
        lastScrollTimeRef.current = Date.now();

        // Verify scroll position after a delay and retry if needed
        setTimeout(() => {
          const expectedBottom = contentHeight - layoutHeight;
          const tolerance = 50; // Allow 50px tolerance
          
          if (Math.abs(currentScrollOffsetRef.current - expectedBottom) > tolerance) {
            console.log('Scroll verification failed, retrying...', {
              current: currentScrollOffsetRef.current,
              expected: expectedBottom,
              difference: Math.abs(currentScrollOffsetRef.current - expectedBottom)
            });
            attemptScroll(attempt + 1);
          } else {
            console.log('Scroll to bottom successful');
          }
        }, 300);
      }
    };

    // Start the scroll attempt
    attemptScroll(0);
  }, [messages.length]);

  // Function to check if content is scrollable and show button accordingly
  const checkAndShowScrollButton = useCallback((contentOffset, contentSize, layoutMeasurement, forceShow = false) => {
    const scrollableHeight = contentSize.height - layoutMeasurement.height;
    const isAtBottom = contentOffset.y >= scrollableHeight - 100; // 100px threshold
    const shouldShowButton = contentSize.height > layoutMeasurement.height + 150; // Reduced threshold for better detection

    setIsNearBottom(isAtBottom);
    
    // Show button if content is scrollable and either:
    // 1. User is not at bottom (normal behavior)
    // 2. forceShow is true (for initial load)
    const shouldShow = shouldShowButton && (forceShow || !isAtBottom);

    if (shouldShow !== showScrollToBottom) {
      setShowScrollToBottom(shouldShow);

      // Animate button visibility
      Animated.timing(scrollToBottomOpacity, {
        toValue: shouldShow ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      console.log('Scroll button visibility changed:', {
        shouldShow,
        shouldShowButton,
        isAtBottom,
        forceShow,
        contentHeight: contentSize.height,
        layoutHeight: layoutMeasurement.height,
        scrollableHeight
      });
    }
  }, [showScrollToBottom, scrollToBottomOpacity]);

  // Enhanced function to check scroll position with better accuracy
  const checkScrollPosition = useCallback((event) => {
    const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
    
    // Update our refs with current values
    currentScrollOffsetRef.current = contentOffset.y;
    contentSizeRef.current = { 
      width: contentSize.width, 
      height: contentSize.height 
    };
    flatListLayoutRef.current = { 
      width: layoutMeasurement.width, 
      height: layoutMeasurement.height 
    };

    // Check and show scroll button (normal behavior, not forced)
    checkAndShowScrollButton(contentOffset, contentSize, layoutMeasurement, false);
  }, [checkAndShowScrollButton]);

  // Function to handle message deletion
  const handleDeleteMessage = (messageId) => {
    // Find the message to delete
    const messageIndex = messages.findIndex(msg => msg.id === messageId);

    if (messageIndex === -1) return; // Message not found

    // Create a copy of the messages array
    const updatedMessages = [...messages];

    // Remove the message
    updatedMessages.splice(messageIndex, 1);

    // Update the messages state
    setMessages(updatedMessages);
  };

  // Cleanup loading state when user enters the page
  useEffect(() => {
    cleanupLoadingStateOnEntry();
  }, [cleanupLoadingStateOnEntry]);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim, quickReplies]);

  // Save chat history when component unmounts or messages/quickReplies change
  useEffect(() => {
    // Skip the initial render to prevent infinite loop
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }

    if (examCode && qnaId) {
      // Use a debounce to prevent too frequent updates
      /* const timeoutId = setTimeout(() => {
        updateChatHistory(examCode, qnaId, messages, quickReplies);
      }, 300);

      return () => clearTimeout(timeoutId); */
    }
  }, [examCode, qnaId, messages, quickReplies/* , updateChatHistory */]);

  // Log route params for debugging
  useEffect(() => {
    /* console.log('ChatScreen opened with params:', { examCode, qnaId });
    console.log('Initial chat state:', {
      messageCount: initialChatState.messages.length,
      quickRepliesCount: initialChatState.quickReplies.length
    }); */
  }, [examCode, qnaId, initialChatState]);

  // Enhanced content size change handler with improved measurement tracking
  const handleContentSizeChange = useCallback((contentWidth, contentHeight) => {
    // Store previous height for comparison
    const previousHeight = contentSizeRef.current.height;

    // Update content size ref for scroll calculations
    contentSizeRef.current = { width: contentWidth, height: contentHeight };

    // Log only significant changes to reduce console noise
    if (Math.abs(contentHeight - previousHeight) > 10) {
      console.log('Content size significantly changed:', {
        contentWidth,
        contentHeight,
        heightDifference: contentHeight - previousHeight,
        layoutHeight: flatListLayoutRef.current.height,
        isInitialLoad: !hasCompletedInitialLoad.current,
        hasCompletedInitialLoad: hasCompletedInitialLoad.current
      });
    }

    // Always check if we should show scroll button when content size changes
    if (flatListLayoutRef.current.height > 0) {
      const mockContentOffset = { y: currentScrollOffsetRef.current };
      const mockContentSize = { width: contentWidth, height: contentHeight };
      const mockLayoutMeasurement = flatListLayoutRef.current;
      
      // Force show button if content is scrollable and initial load is complete
      const forceShow = !hasCompletedInitialLoad.current;
      checkAndShowScrollButton(mockContentOffset, mockContentSize, mockLayoutMeasurement, forceShow);
    }

    // Don't auto-scroll during initial page load
    if (!hasCompletedInitialLoad.current) {
      console.log('Skipping auto-scroll during initial page load');
      return;
    }

    // Auto-scroll to bottom when new content is added and user is near bottom
    if (contentHeight > previousHeight && isNearBottom) {
      // Use a longer delay to ensure content is fully rendered
      setTimeout(() => {
        scrollToBottom();
      }, 150);
    }
  }, [isNearBottom, scrollToBottom, checkAndShowScrollButton]);

  // Enhanced scroll handler that tracks user-initiated scrolls
  const handleScroll = useCallback((event) => {
    // Update current scroll position
    currentScrollOffsetRef.current = event.nativeEvent.contentOffset.y;
    
    // Check scroll position for showing/hiding scroll-to-bottom button
    checkScrollPosition(event);

    // Check if this is a user-initiated scroll (not our programmatic scrolling)
    const currentTime = Date.now();
    const timeSinceLastAutoScroll = currentTime - lastScrollTimeRef.current;

    // If it's been more than 500ms since our last programmatic scroll,
    // we can assume this is a user-initiated scroll
    if (timeSinceLastAutoScroll > 500) {
      // User has manually scrolled - we can track this if needed
    }
  }, [checkScrollPosition]);

  // Track retry attempts to prevent infinite loops
  const retryCountRef = useRef(0);
  const MAX_RETRIES = 2; // Maximum number of automatic retries

  const handleUserInput = async (userText, isRetry = false) => {
    if (!userText.trim()) return;

    // Reset retry counter if this is a new request (not a retry)
    if (!isRetry) {
      retryCountRef.current = 0;
    }

    // Track when the request started
    const requestStartTime = Date.now();

    // Check if user is logged in
    if (!isLoggedIn) {
      setLoginModalVisible(true);
      return;
    }

    // Check if user has enough credits
    if (credits <= 0) {
      Alert.alert(
        'Out of Credits',
        'You have run out of AI credits. Please refill your credits to continue using the AI assistant.',
        [
          {
            text: 'Refill Credits',
            onPress: () => setCreditModalVisible(true)
          },
          {
            text: 'Cancel',
            style: 'cancel'
          }
        ]
      );
      return;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      clearTimeout(requestTimeoutRef.current);
    }

    // We'll deduct credits only after a successful API response
    // Create user message
    const userMessage = {
      id: Date.now().toString(),
      choices: [
        {
          index: 0,
          message: {
            role: 'user',
            content: userText,
          },
          logprobs: null,
          finish_reason: 'stop',
        },
      ],
    };

    const placeholderAssistantMessage = {
      id: `loading-${Date.now().toString()}`,
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: '...',
            isLoading: true,
          },
          logprobs: null,
          finish_reason: 'stop',
        },
      ],
    };

    // Store the loading message ID for cleanup
    currentLoadingMessageIdRef.current = placeholderAssistantMessage.id;

    setMessages(prev => [...prev, userMessage, placeholderAssistantMessage]);
    setLoading(true);

    // Scroll to bottom when user sends a message to show the loading indicator
    setTimeout(() => {
      scrollToBottom();
    }, 100);

    // Create a new AbortController for this request
    abortControllerRef.current = new AbortController();

    // Set a timeout to automatically cancel the request if it takes too long
    requestTimeoutRef.current = setTimeout(() => {
      // Abort the request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      // Remove both the placeholder message and the user message
      setMessages(prev =>
        prev.filter(msg => 
          msg.id !== placeholderAssistantMessage.id && 
          msg.id !== userMessage.id
        )
      );
      
      // Clear the loading message ref
      currentLoadingMessageIdRef.current = null;
      setLoading(false);

      // Notify the user with an alert dialog
      Alert.alert(
        'Request Timeout',
        'The AI is taking too long to respond. Please try again later.',
        [{ text: 'OK' }]
      );
    }, 15000); // 15-second timeout

    try {
      // Construct the payload for the AI API request
      let payload;

      // Get the specific chat history for this question from context
      const currentChatHistory = getChatHistory(examCode, qnaId);

      // Determine if this is the initial request by checking if there are only system messages
      // or if there's only the welcome message in the history
      const hasOnlyWelcomeMessage = currentChatHistory.messages.length === 1 &&
        currentChatHistory.messages[0].id === 'welcome-message';
      const hasNoUserMessages = !currentChatHistory.messages.some(msg =>
        msg.choices[0].message.role === 'user');

      const isInitialRequest = hasOnlyWelcomeMessage || hasNoUserMessages || messages.length <= 1;
      let formattedHistory = [];

      // Build question-specific context
      const questionContext = currentQuestion ? {
        question: currentQuestion?.content || '',
        choices: currentQuestion?.choices || [],
        correctAnswer: currentQuestion?.answer || ''
      } : null;

      if (isInitialRequest) {
        // Initial request - include full question context and user's query as the question
        payload = {
          question: userText, // Use the user's input as the question
          systemPrompt: questionContext,
          userQuery: userText,
          isInitialRequest: true, // Explicitly set to true for initial requests
          questionId: qnaId // Add question identifier
        };

        console.log('Sending initial request with user query:', userText);
      } else {
        // Follow-up request - use only the history for this specific question
        // Filter out loading messages, welcome message, and ensure we only include completed exchanges
        const filteredMessages = currentChatHistory.messages.filter(msg =>
          !msg.choices[0].message.isLoading &&
          msg.id !== 'welcome-message' &&
          msg.choices[0].message.content.trim() !== ''
        );

        // Format the chat history properly
        formattedHistory = filteredMessages.map(msg => ({
          role: msg.choices[0].message.role,
          content: msg.choices[0].message.content,
          timestamp: msg.id
        }));

        // Keep the current user message separate from history
        // This ensures the latest user message is properly identified as the current query
        payload = {
          question: userText, // Use the user's input as the question
          systemPrompt: questionContext,
          chatHistory: formattedHistory,
          userQuery: userText, // Current user message as the query
          isInitialRequest: false,
          questionId: qnaId // Add question identifier
        };
      }

      // Add detailed debug logging for the payload
      console.log('Sending API payload:', {
        question: payload.question,
        userQuery: payload.userQuery,
        isInitialRequest: payload.isInitialRequest,
        questionId: payload.questionId,
        // Omit large fields from console for readability
        systemPrompt: isInitialRequest ? '[system prompt included]' : undefined,
        chatHistory: !isInitialRequest ? `[${formattedHistory.length} messages]` : undefined,
        hasCurrentQuestion: currentQuestion !== null,
        messageCount: messages.length
      });

      const response = await apiClient.getAIResponse(payload, {
        signal: abortControllerRef.current.signal,
        skipTimeoutAlert: true, // We handle our own timeout alerts
      });

      // Clear the timeout since we got a response
      clearTimeout(requestTimeoutRef.current);

      console.log('apiClient.url:', apiClient.baseUrl);
      console.log('API Response:', JSON.stringify(response, null, 2));

      // Now that we have a successful response, deduct 1 credit
      const creditDeducted = await useCredits(1);
      if (creditDeducted) {
        console.log('Credit deducted after successful API response');
      } else {
        console.warn('Failed to deduct credit, but will still show response');
      }

      // Preserve the original formatting from the API
      const formattedAnswer = response.answer || '';

      const aiMessage = {
        id: Date.now().toString(),
        choices: [
          {
            index: 0,
            message: {
              role: 'assistant',
              content: formattedAnswer,
              isLoading: false,
              // Pass the raw answer as metadata
              metadata: {
                formattedAnswer: formattedAnswer,
                formattedExplanation: '' // We don't need separate explanation as it's included in the answer
              }
            },
            logprobs: null,
            finish_reason: 'stop',
          },
        ],
      };

      setMessages(prev =>
        prev.map(msg =>
          msg.id === placeholderAssistantMessage.id ? aiMessage : msg,
        ),
      );

      // Clear the loading message ref since it's been replaced
      currentLoadingMessageIdRef.current = null;

      setQuickReplies(response.follow_up_questions || []);

      // Note: Removed automatic scroll to bottom when AI response arrives
      // Users can manually scroll using the floating scroll-to-bottom button if needed
    } catch (error) {
      // Clear the timeout since we got an error
      clearTimeout(requestTimeoutRef.current);

      // Only handle the error if it's not an abort error (which we triggered ourselves)
      if (error.name !== 'AbortError') {
        console.error('AI request failed:', error);

        // Determine the error message based on the error type
        let errorTitle = 'Error';
        let errorMessage = 'Sorry, there was an error processing your request. Please try again.';
        let canRetry = true;

        if (error.status === 401 || error.status === 403) {
          errorTitle = 'Authentication Required';
          errorMessage = 'Your session has expired. Please log in again to continue using the AI assistant.';
          canRetry = false; // Don't auto-retry auth errors
        } else if (error.message && (error.message.includes('network') || error.message.includes('Network'))) {
          errorTitle = 'Network Error';
          errorMessage = 'Please check your internet connection and try again.';
        } else if (error.message && error.message.includes('timeout')) {
          errorTitle = 'Request Timeout';
          errorMessage = 'The server took too long to respond. Please try again later.';
        } else if (error.status === 429) {
          errorTitle = 'Too Many Requests';
          errorMessage = 'You have made too many requests. Please wait a moment and try again.';
          canRetry = false; // Don't auto-retry rate limit errors
        } else if (error.status >= 500) {
          errorTitle = 'Server Error';
          errorMessage = 'Our servers are experiencing issues. Please try again later.';
          canRetry = retryCountRef.current < 1; // Only retry server errors once
        }

        // Remove both the loading placeholder and the last user message
        setMessages(prev =>
          prev.filter(msg => 
            msg.id !== placeholderAssistantMessage.id && 
            msg.id !== userMessage.id
          )
        );

        // Clear the loading message ref
        currentLoadingMessageIdRef.current = null;

        // Show appropriate alert based on error type
        if (error.status === 401 || error.status === 403) {
          // For authentication errors, show login prompt
          Alert.alert(
            errorTitle,
            errorMessage,
            [
              {
                text: 'Login',
                onPress: () => {
                  setLoginModalVisible(true);
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
        } else {
          // For other errors, show standard retry options
          Alert.alert(
            errorTitle,
            errorMessage,
            [
              {
                text: 'Try Again',
                onPress: () => {
                  // Reset retry counter for manual retry
                  retryCountRef.current = 0;
                  handleUserInput(userText, false);
                }
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
        }
      } else {
        // For abort errors (user left page), just clear the loading message ref
        currentLoadingMessageIdRef.current = null;
      }
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    quickReplyContainer: {
      flexDirection: 'column',
      justifyContent: 'space-around',
      marginBottom: 50,
      paddingHorizontal: 10,
    },
    quickReplyButton: {
      backgroundColor: theme.colors.background,
      padding: 10,
      borderRadius: 20,
      marginBottom: 10,
      borderColor: theme.colors.border,
      borderWidth: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    quickReplyText: {
      color: theme.colors.assistanceText,
      width: '90%'
    },
    icon: {
      position: 'absolute',
      right: 12,
    },
    scrollToBottomButton: {
      position: 'absolute',
      right: 20,
      bottom: 120, // Position above the ChatBox and ads
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: theme.colors.primary,
      elevation: 8,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    scrollToBottomTouchable: {
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 25,
    },
  });

  // Save chat history when component unmounts
  useEffect(() => {
    return () => {
      // Save chat history
      if (examCode && qnaId) {
        updateChatHistory(examCode, qnaId, messages, quickReplies);
      }
    };
  }, [examCode, qnaId, messages, quickReplies]);

  return (
    <View style={styles.container}>
      <AIChatHeader title="Ask AI" />
      <FlatList style={{flex: 1}}
        ref={flatListRef}
        data={messages}
        keyExtractor={item => item.id}
        // Improve performance with optimized rendering
        removeClippedSubviews={false} // Keep all items mounted for better measurement accuracy
        maxToRenderPerBatch={10} // Render more items per batch for smoother scrolling
        windowSize={10} // Increase window size for better offscreen rendering
        // Maintain scroll position when content changes
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10 // Auto-scroll to top when items are added at the top
        }}
        renderItem={({ item }) => (
          <RenderMessage
            item={item}
            loading={loading}
            onDeleteMessage={handleDeleteMessage}
            onRetry={(errorItem) => {
              // Get the original user message that caused this error
              const userMessageIndex = messages.findIndex(msg =>
                msg.choices[0].message.role === 'user' &&
                messages.indexOf(msg) < messages.indexOf(errorItem)
              );

              if (userMessageIndex >= 0) {
                const userMessage = messages[userMessageIndex];
                const userText = userMessage.choices[0].message.content;

                // Remove the error message before retrying
                setMessages(prev =>
                  prev.filter(msg => msg.id !== errorItem.id)
                );

                // Reset retry counter for manual retry
                retryCountRef.current = 0;

                // Retry the request with the original user text
                handleUserInput(userText, false);
              }
            }}
          />
        )}
        onContentSizeChange={handleContentSizeChange}
        onLayout={(event) => {
          // Capture FlatList dimensions for scroll calculations
          const { width, height } = event.nativeEvent.layout;
          flatListLayoutRef.current = { width, height };

          console.log('FlatList layout updated:', { width, height });

          // Mark initial render as complete and allow auto-scrolling for future interactions
          if (isInitialRender.current) {
            isInitialRender.current = false;
            // Set a timeout to mark initial load as complete after layout is done
            setTimeout(() => {
              hasCompletedInitialLoad.current = true;
              console.log('Initial page load completed, auto-scroll now enabled');
              
              // Check if we should show scroll button after initial load
              if (contentSizeRef.current.height > 0) {
                const mockContentOffset = { y: currentScrollOffsetRef.current };
                const mockContentSize = contentSizeRef.current;
                const mockLayoutMeasurement = { width, height };
                
                // Force show button if content is scrollable during initial load
                checkAndShowScrollButton(mockContentOffset, mockContentSize, mockLayoutMeasurement, true);
              }
            }, 200); // Reduced timeout for faster button appearance
          }
        }}
        onScroll={handleScroll}
        scrollEventThrottle={16} // 60fps for smooth scrolling
        onScrollToIndexFailed={(info) => {
          // Handle scroll to index failures gracefully
          console.warn('Scroll to index failed:', info);
          // Fallback to scrollToEnd
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: true });
          }, 100);
        }}
        ListFooterComponent={
          <>
            <View style={[{height: 20}]}/>
            {!loading && quickReplies.length > 0 && (
              <Animated.View style={[styles.quickReplyContainer, { opacity: fadeAnim }]}>
                {quickReplies.map((reply, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.quickReplyButton}
                    onPress={() => handleUserInput(reply)}
                  >
                    <Text style={styles.quickReplyText}>{reply}</Text>
                    <AntDesign
                      style={styles.icon}
                      name="arrowright"
                      color={theme.colors.onBackground}
                      size={24}
                    />
                  </TouchableOpacity>
                ))}
              </Animated.View>
            )}
          </>
        }
      />

      <StickyBottomAdMob subscriptionActive={subscriptionActive} />
      <ChatBox
        inputMessage={inputMessage}
        setInputMessage={setInputMessage}
        onHandleSend={() => {
          handleUserInput(inputMessage);
          setInputMessage('');
        }}
        disabled={credits <= 0 || !isLoggedIn}
      />

      {/* Floating Scroll to Bottom Button */}
      {showScrollToBottom && (
        <Animated.View
          style={[
            styles.scrollToBottomButton,
            {
              opacity: scrollToBottomOpacity,
            }
          ]}
        >
          <TouchableOpacity
            onPress={scrollToBottom}
            style={styles.scrollToBottomTouchable}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="keyboard-arrow-down"
              size={28}
              color={theme.colors.onPrimary}
            />
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* AI Credit Modal */}
      <AICreditModal
        visible={creditModalVisible}
        onDismiss={() => setCreditModalVisible(false)}
      />

      {/* Login Required Modal */}
      <LoginModal
        visible={loginModalVisible}
        onDismiss={() => setLoginModalVisible(false)}
        onLoginSuccess={() => {
          setLoginModalVisible(false);
        }}
        source="aiChat"
      />
    </View>

  );
};

export default ChatScreen;