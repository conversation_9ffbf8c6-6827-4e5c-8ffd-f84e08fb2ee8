#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Get command line arguments
const appEnv = process.argv[2]; // 'dev' or 'release'
const appVariant = process.argv[3]; // 'aws' or 'pmi'
const command = process.argv[4]; // 'start' or 'run-android'
const additionalArgs = process.argv.slice(5); // Any additional arguments

if (!appEnv || !appVariant || !command) {
    console.error('Usage: node set-dev-env.js <app_env> <app_variant> <command> [additional_args...]');
    console.error('Example: node set-dev-env.js dev aws start');
    console.error('Example: node set-dev-env.js dev aws run-android --mode=awsDebug --appId=com.prepfy.qna');
    process.exit(1);
}

// Function to load environment file
function loadEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`Environment file not found: ${filePath}`);
        process.exit(1);
    }

    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const [key, ...valueParts] = line.split('=');
            if (key && valueParts.length > 0) {
                envVars[key.trim()] = valueParts.join('=').trim();
            }
        }
    });

    return envVars;
}

// Determine the environment file to load
let envFile;
if (appEnv === 'dev' && appVariant === 'aws') {
    envFile = '.env.dev';
} else if (appEnv === 'dev' && appVariant === 'pmi') {
    envFile = '.env.pmi.dev';
} else if (appEnv === 'release' && appVariant === 'aws') {
    envFile = '.env';
} else if (appEnv === 'release' && appVariant === 'pmi') {
    envFile = '.env.pmi';
} else {
    console.error(`Invalid combination: APP_ENV=${appEnv}, APP_VARIANT=${appVariant}`);
    process.exit(1);
}

console.log(`Setting development environment: APP_ENV=${appEnv}, APP_VARIANT=${appVariant}`);
console.log(`Loading environment from: ${envFile}`);

// Load environment variables
const envVars = loadEnvFile(envFile);

// Create environment for child process
const childEnv = { ...process.env };

// Set APP_ENV and APP_VARIANT for babel configuration
childEnv.APP_ENV = appEnv;
childEnv.APP_VARIANT = appVariant;

// Set all environment variables from the file
Object.keys(envVars).forEach(key => {
    childEnv[key] = envVars[key];
    console.log(`Set ${key}=${envVars[key]}`);
});

console.log('Environment variables set successfully');

// Determine the command to run
let cmdArgs;
if (command === 'start') {
    cmdArgs = ['react-native', 'start', '--reset-cache'];
} else if (command === 'run-android') {
    cmdArgs = ['react-native', 'run-android', ...additionalArgs];
} else {
    console.error(`Unknown command: ${command}`);
    process.exit(1);
}

console.log(`Running: npx ${cmdArgs.join(' ')}`);

// Spawn the React Native command with the environment variables
const child = spawn('npx', cmdArgs, {
    env: childEnv,
    stdio: 'inherit',
    shell: true
});

child.on('close', (code) => {
    process.exit(code);
});
