#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 3) {
  console.error('Usage: node build-with-env.js <buildType> <vendor> <variant> [--no-copy]');
  console.error('Examples:');
  console.error('  node build-with-env.js dev aws debug');
  console.error('  node build-with-env.js release pmi release');
  console.error('  node build-with-env.js debug pmi debug --no-copy');
  process.exit(1);
}

const [buildType, vendor, variant] = args;
const noCopy = args.includes('--no-copy');

// Determine environment file based on buildType and vendor
function getEnvFile(buildType, vendor) {
  if (buildType === 'dev') {
    return vendor === 'pmi' ? '.env.pmi.dev' : '.env.dev';
  } else if (buildType === 'debug') {
    return vendor === 'pmi' ? '.env.pmi.dev' : '.env.dev';
  } else if (buildType === 'release') {
    return vendor === 'pmi' ? '.env.pmi' : '.env';
  }
  throw new Error(`Unknown buildType: ${buildType}`);
}

// Load environment variables from file
function loadEnvFile(envFile) {
  const envPath = path.resolve(envFile);
  if (!fs.existsSync(envPath)) {
    throw new Error(`Environment file not found: ${envPath}`);
  }

  console.log(`Loading environment from: ${envFile}`);
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  console.log('Loaded environment variables:');
  Object.keys(envVars).forEach(key => {
    console.log(`  ${key}=${envVars[key]}`);
  });

  return envVars;
}

// Execute command with environment variables
function execWithEnv(command, envVars, options = {}) {
  const env = { ...process.env, ...envVars };
  console.log(`\nExecuting: ${command}`);
  try {
    return execSync(command, { 
      stdio: 'inherit', 
      env,
      ...options 
    });
  } catch (error) {
    console.error(`Command failed: ${command}`);
    throw error;
  }
}

// Main build process
async function main() {
  try {
    console.log(`\n🚀 Starting build process:`);
    console.log(`  Build Type: ${buildType}`);
    console.log(`  Vendor: ${vendor}`);
    console.log(`  Variant: ${variant}`);
    console.log(`  Copy APK: ${!noCopy}`);

    // Step 1: Load environment variables
    const envFile = getEnvFile(buildType, vendor);
    const envVars = loadEnvFile(envFile);

    // Step 2: Create React Native bundle
    console.log(`\n📦 Creating React Native bundle...`);
    let bundleCommand;

    if (vendor === 'pmi') {
      if (buildType === 'debug' || buildType === 'dev') {
        bundleCommand = `node build-bundle.js ${vendor} debug`;
      } else {
        bundleCommand = `node build-bundle.js ${vendor}`;
      }
    } else {
      // AWS
      if (buildType === 'dev' || buildType === 'debug') {
        bundleCommand = `node build-bundle.js dev debug`;
      } else {
        bundleCommand = 'node build-bundle.js';
      }
    }
    
    execWithEnv(bundleCommand, envVars);

    // Step 3: Build Android APK with same environment variables
    console.log(`\n🔨 Building Android APK...`);

    // Determine Gradle task
    const gradleTask = `assemble${vendor.charAt(0).toUpperCase() + vendor.slice(1)}${variant.charAt(0).toUpperCase() + variant.slice(1)}`;

    // Clean and build (use gradlew.bat on Windows)
    const gradlewCmd = process.platform === 'win32' ? 'gradlew.bat' : './gradlew';

    // Pass environment variables to Gradle via system properties
    const gradleProps = `-Dapp.variant=${vendor} -Dapp.env=${envVars.APP_ENV || 'release'}`;

    execWithEnv(`${gradlewCmd} clean ${gradleProps}`, envVars, { cwd: 'android' });
    execWithEnv(`${gradlewCmd} ${gradleTask} ${gradleProps}`, envVars, { cwd: 'android' });

    // Step 4: Copy APK to Google Drive (if not --no-copy)
    if (!noCopy) {
      console.log(`\n📋 Copying APK to Google Drive...`);
      const apkPath = `app\\build\\outputs\\apk\\${vendor}\\${variant}\\app-${vendor}-${variant}.apk`;
      const copyCommand = `copy "${apkPath}" "G:\\我的雲端硬碟\\projects\\Edump\\"`;
      execWithEnv(copyCommand, envVars, { cwd: 'android' });
    }

    console.log(`\n✅ Build completed successfully!`);
    console.log(`   Environment: ${envFile}`);
    console.log(`   APK: android/app/build/outputs/apk/${vendor}/${variant}/app-${vendor}-${variant}.apk`);
    
  } catch (error) {
    console.error(`\n❌ Build failed:`, error.message);
    process.exit(1);
  }
}

main();
