#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to load environment variables from a file
function loadEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`Environment file not found: ${filePath}`);
        process.exit(1);
    }
    
    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const [key, ...valueParts] = line.split('=');
            if (key && valueParts.length > 0) {
                let value = valueParts.join('=').trim();
                // Remove comments from the end of the line
                const commentIndex = value.indexOf('#');
                if (commentIndex !== -1) {
                    value = value.substring(0, commentIndex).trim();
                }
                envVars[key.trim()] = value;
            }
        }
    });
    
    return envVars;
}

// Get the flavor from command line arguments
const flavor = process.argv[2];
if (!flavor) {
    console.error('Usage: node build-bundle.js <flavor>');
    console.error('Example: node build-bundle.js pmi');
    process.exit(1);
}

// Load the environment file for the flavor
const envFile = `.env.${flavor}`;
console.log(`Loading environment from: ${envFile}`);
const envVars = loadEnvFile(envFile);

// Set environment variables
Object.keys(envVars).forEach(key => {
    process.env[key] = envVars[key];
    console.log(`Set ${key}=${envVars[key]}`);
});

// Create the bundle
const bundleCommand = `npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/`;

console.log('\nCreating React Native bundle...');
console.log(`Command: ${bundleCommand}`);

try {
    execSync(bundleCommand, { stdio: 'inherit' });
    console.log('\nBundle created successfully!');
} catch (error) {
    console.error('Failed to create bundle:', error.message);
    process.exit(1);
}
