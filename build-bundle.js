#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to load environment variables from a file
function loadEnvFile(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`Environment file not found: ${filePath}`);
        process.exit(1);
    }

    const envContent = fs.readFileSync(filePath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
            const [key, ...valueParts] = line.split('=');
            if (key && valueParts.length > 0) {
                let value = valueParts.join('=').trim();
                // Remove comments from the end of the line
                const commentIndex = value.indexOf('#');
                if (commentIndex !== -1) {
                    value = value.substring(0, commentIndex).trim();
                }
                envVars[key.trim()] = value;
            }
        }
    });

    return envVars;
}

// Get the flavor and build type from command line arguments
const flavor = process.argv[2];
const buildType = process.argv[3]; // 'debug' or 'release'

// Determine the environment file to load and set APP_ENV/APP_VARIANT
let envFile;
let appEnv = 'release';
let appVariant = 'aws';

if (!flavor) {
    // No flavor specified - use default .env (for AWS release builds)
    envFile = '.env';
    appEnv = 'release';
    appVariant = 'aws';
    console.log('No flavor specified, using default environment file for AWS release');
} else if (flavor === 'dev') {
    // Dev flavor - use .env.dev (for AWS debug builds)
    envFile = '.env.dev';
    appEnv = 'dev';
    appVariant = 'aws';
    console.log('Using AWS development environment file');
} else if (flavor === 'pmi' && buildType === 'debug') {
    // PMI debug builds - use .env.pmi.dev
    envFile = '.env.pmi.dev';
    appEnv = 'dev';
    appVariant = 'pmi';
    console.log('Using PMI development environment file');
} else if (flavor === 'pmi') {
    // PMI release builds - use .env.pmi
    envFile = '.env.pmi';
    appEnv = 'release';
    appVariant = 'pmi';
    console.log('Using PMI release environment file');
} else {
    // Other specific flavors - use .env.<flavor>
    envFile = `.env.${flavor}`;
    appEnv = 'release';
    appVariant = flavor;
    console.log(`Using custom environment file for flavor: ${flavor}`);
}

console.log(`Loading environment from: ${envFile}`);
console.log(`Setting APP_ENV=${appEnv}, APP_VARIANT=${appVariant}`);

// Set APP_ENV and APP_VARIANT for babel configuration
process.env.APP_ENV = appEnv;
process.env.APP_VARIANT = appVariant;

const envVars = loadEnvFile(envFile);

// Set environment variables
Object.keys(envVars).forEach(key => {
    process.env[key] = envVars[key];
    console.log(`Set ${key}=${envVars[key]}`);
});

// Create the bundle
const bundleCommand = `npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/`;

console.log('\nCreating React Native bundle...');
console.log(`Command: ${bundleCommand}`);



try {
    // Pass all environment variables to the bundle command
    const bundleEnv = { ...process.env };
    execSync(bundleCommand, { stdio: 'inherit', env: bundleEnv });
    console.log('\nBundle created successfully!');
} catch (error) {
    console.error('Failed to create bundle:', error.message);
    process.exit(1);
}