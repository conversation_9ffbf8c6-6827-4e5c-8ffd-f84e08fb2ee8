{"name": "com.prepfy.qna", "version": "1.0.0", "private": true, "scripts": {"android": "node scripts/set-dev-env.js release aws run-android --mode=awsRelease --appId=com.prepfy.qna", "android:dev": "node scripts/set-dev-env.js dev aws run-android --mode=awsDebug --appId=com.prepfy.qna", "android:pmi": "node scripts/set-dev-env.js release pmi run-android --mode=pmiRelease --appId=com.prepfy.pmi --main-activity=com.prepfy.pmi.MainActivity", "android:pmi:dev": "node scripts/set-dev-env.js dev pmi run-android --mode=pmiDebug --appId=com.prepfy.pmi --main-activity=com.prepfy.pmi.MainActivity", "ios": "cross-env APP_VARIANT=aws react-native run-ios --scheme TestApp", "ios:pmi": "cross-env APP_VARIANT=pmi react-native run-ios --scheme TestApp-PMI", "lint": "eslint .", "start": "node scripts/set-dev-env.js release aws start", "start:pmi": "node scripts/set-dev-env.js release pmi start", "dev": "node scripts/set-dev-env.js dev aws start", "dev:pmi": "node scripts/set-dev-env.js dev pmi start", "test": "jest", "build:android:aws:debug": "node build-bundle.js dev debug && cd android && ./gradlew clean && ./gradlew assembleAwsDebug && copy app\\build\\outputs\\apk\\aws\\debug\\app-aws-debug.apk \"G:\\我的雲端硬碟\\projects\\Edump\\\"", "build:android:aws:release": "node build-bundle.js && cd android && ./gradlew clean && ./gradlew assembleAwsRelease && copy app\\build\\outputs\\apk\\aws\\release\\app-aws-release.apk \"G:\\我的雲端硬碟\\projects\\Edump\\\"", "build:android:aws:apk": "node build-bundle.js && cd android && ./gradlew clean && ./gradlew assembleAwsRelease", "build:android:pmi:debug": "node build-bundle.js pmi debug && cd android && ./gradlew clean && ./gradlew assemblePmiDebug && copy app\\build\\outputs\\apk\\pmi\\debug\\app-pmi-debug.apk \"G:\\我的雲端硬碟\\projects\\Edump\\\"", "build:android:pmi:release": "node build-bundle.js pmi && cd android && ./gradlew clean && ./gradlew assemblePmiRelease && copy app\\build\\outputs\\apk\\pmi\\release\\app-pmi-release.apk \"G:\\我的雲端硬碟\\projects\\Edump\\\"", "build:android:pmi:apk": "node build-bundle.js pmi && cd android && ./gradlew clean && ./gradlew assemblePmiRelease", "build:android:aws:aab": "node build-bundle.js && cd android && ./gradlew clean && ./gradlew bundleAwsRelease && echo \"AWS AAB file generated at: $(pwd)/app/build/outputs/bundle/awsRelease/app-aws-release.aab\"", "build:android:pmi:aab": "node build-bundle.js pmi && cd android && ./gradlew clean && ./gradlew bundlePmiRelease && echo \"PMI AAB file generated at: $(pwd)/app/build/outputs/bundle/pmiRelease/app-pmi-release.aab\"", "debug": "react-native bundle --platform android --dev true --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew clean && ./gradlew assembleDebug && cd .. && cp -f android/app/build/outputs/apk/debug/app-debug.apk \"G:/我的雲端硬碟/projects/Edump\"", "release": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew clean && ./gradlew assembleRelease && cd .. && cp -f android/app/build/outputs/apk/release/app-release.apk \"G:/我的雲端硬碟/projects/Edump/aws.apk\"", "aab": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew clean && ./gradlew bundleRelease && cd .. && echo \"AAB file generated at: android/app/build/outputs/bundle/release/app-release.aab\"", "generate-icon-and-bootsplash": "npx rn-ml appicon -s \"G:\\我的雲端硬碟\\projects\\Edump\\aws icon for play store 512 512.png\" --flavor aws  && yarn react-native-bootsplash generate \"G:\\我的雲端硬碟\\projects\\Edump\\aws icon splash.png\" --background=131426 --logo-width=140"}, "dependencies": {"@dotenvx/dotenvx": "^1.40.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^21.14.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/material-top-tabs": "^7.1.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.1", "@react-navigation/stack": "^7.1.1", "date-fns": "^4.1.0", "react": "18.3.1", "react-native": "0.77.1", "react-native-app-auth": "^8.0.3", "react-native-bootsplash": "^6.3.3", "react-native-calendar-picker": "^8.0.5", "react-native-circular-progress-indicator": "^4.4.2", "react-native-copilot": "^3.3.3", "react-native-device-info": "^14.0.4", "react-native-dotenv": "^3.4.11", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.25.0", "react-native-google-mobile-ads": "^15.3.1", "react-native-keychain": "^10.0.0", "react-native-pager-view": "^6.7.0", "react-native-paper": "^5.13.1", "react-native-purchases": "^8.10.1", "react-native-reanimated": "^3.16.7", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.2.0", "react-native-screens": "^4.7.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "cross-env": "^7.0.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}