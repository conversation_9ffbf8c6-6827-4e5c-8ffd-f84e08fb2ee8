apply from: "env-loader.gradle"

apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"
apply plugin: 'com.google.gms.google-services'

// Load keystore properties if the file exists
def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

// Helper function (can be in env-loader.gradle)
/* def loadEnvForVariant(flavor, buildType, envFile) {
    def envVars = [:]
    def envFileObj = rootProject.file(envFile)
    if (envFileObj.exists()) {
        envFileObj.eachLine { line ->
            if (line && !line.startsWith("#")) {
                def idx = line.indexOf("=")
                if (idx > 0) {
                    def key = line.substring(0, idx).trim()
                    def value = line.substring(idx + 1).trim()
                    envVars[key] = value
                }
            }
        }
    }
    return envVars
} */

// Safe environment variable getter function
def safeGetEnvVar = { String key, String defaultValue = "" ->
    def envVars = ext.has('envVars') ? ext.envVars : [:]
    def value = envVars.get(key, defaultValue)
    return value != null ? value : defaultValue
}



/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */


react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '../..'
    // root = file("../../")
    //   The folder where the react-native NPM package is. Default is ../../node_modules/react-native
    // reactNativeDir = file("../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../../node_modules/@react-native/codegen
    // codegenDir = file("../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../../node_modules/react-native/cli.js
    // cliFile = file("../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    debuggableVariants = ["awsDebug", "pmiDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]

    /* Autolinking */
    autolinkLibrariesWithApp()
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = true

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

// Get app name from environment variables with proper fallback
def getAppName = { ->
    def isForTester = safeGetEnvVar('IS_FOR_TESTER', 'false')
    def appName = safeGetEnvVar('APP_NAME', 'App')
    
    if (isForTester == 'true') {
        return "QA"
    }
    return appName
}

android {
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace "com.prepfy.qna"
    defaultConfig {
        applicationId "com.prepfy.qna"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 22
        versionName "1.0.23"
        manifestPlaceholders = [appAuthRedirectScheme: "com.prepfy.qna"]
    }
    
    flavorDimensions "variant"
    productFlavors {
        aws {
            dimension "variant"
            applicationId "com.prepfy.qna"
            versionCode 23
            versionName "1.0.24"
            manifestPlaceholders = [
                appAuthRedirectScheme: "com.prepfy.qna",
                appName: "5000+ AWS Dump"
            ]
            resValue "string", "app_name", "5000+ AWS Dump"
            buildConfigField "String", "APP_VARIANT", '"aws"'
        }
        pmi {
            dimension "variant"
            applicationId "com.prepfy.pmi"
            versionCode 3
            versionName "1.0.2"
            manifestPlaceholders = [
                appAuthRedirectScheme: "com.prepfy.pmi",
                appName: "1800+ PMI Dump"
            ]
            resValue "string", "app_name", "1800+ PMI Dump"
            buildConfigField "String", "APP_VARIANT", '"pmi"'
        }
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            // You need to create a release.keystore file with your own credentials
            // Use this command to generate it:
            // keytool -genkeypair -v -storetype PKCS12 -keystore android/app/release.keystore -alias release-key -keyalg RSA -keysize 2048 -validity 10000
            if (keystorePropertiesFile.exists()) {
                // Use values from keystore.properties file
                storeFile file(keystoreProperties.getProperty('storeFile'))
                storePassword keystoreProperties.getProperty('storePassword')
                keyAlias keystoreProperties.getProperty('keyAlias')
                keyPassword keystoreProperties.getProperty('keyPassword')
            } else {
                // Fallback to default values (will not work for actual release builds)
                storeFile file('debug.keystore')
                storePassword 'android'
                keyAlias 'androiddebugkey'
                keyPassword 'android'
            }
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}

// Use androidComponents to set versionCode and versionName per variant/output
android.applicationVariants.all { variant ->
    variant.outputs.each { output ->
        def versionCode = safeGetEnvVar('VERSION_CODE', '')
        def versionName = safeGetEnvVar('VERSION_NAME', '')
        
        if (versionCode && versionCode.isNumber()) {
            output.versionCodeOverride = versionCode.toInteger()
        }
        if (versionName) {
            output.versionNameOverride = versionName
        }
    }

    // Apply environment-specific configurations with safe defaults
    def isForTester = safeGetEnvVar('IS_FOR_TESTER', 'false') == 'true'
    def defaultAppName = variant.flavorName == 'pmi' ? '1800+ PMI Dump' : '5000+ AWS Dump'
    def variantAppName = isForTester ? 'QA' : safeGetEnvVar('APP_NAME', defaultAppName)
    def appId = safeGetEnvVar('APP_ID', variant.applicationId ?: "com.prepfy.qna")
    def admobAppId = safeGetEnvVar('ADMOB_APP_ID', 'ca-app-pub-3940256099942544~3347511713')

    // Set manifest placeholders with safe values
    variant.mergedFlavor.manifestPlaceholders = [
        appAuthRedirectScheme: appId,
        appName: variantAppName,
        ADMOB_APP_ID: admobAppId
    ]

    // Set resource values and build config fields with safe defaults
    variant.resValue "string", "app_name", variantAppName
    variant.buildConfigField "String", "APP_VARIANT", "\"${variant.flavorName}\""
    variant.buildConfigField "String", "APP_NAME", "\"${variantAppName}\""
    variant.buildConfigField "String", "APP_ID", "\"${appId}\""
    variant.buildConfigField "String", "APP_ENV", "\"${variant.buildType.name == "debug" ? "dev" : "release"}\""
    variant.buildConfigField "String", "VENDOR_CODE", "\"${safeGetEnvVar('VENDOR_CODE', 'default')}\""
    variant.buildConfigField "String", "EXAM_TYPE", "\"${safeGetEnvVar('EXAM_TYPE', 'DEFAULT')}\""
    variant.buildConfigField "boolean", "IS_FOR_TESTER", "${safeGetEnvVar('IS_FOR_TESTER', 'false')}"
}




dependencies {
    implementation("com.facebook.react:react-android")
    implementation platform('com.google.firebase:firebase-bom:33.12.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.android.gms:play-services-ads:24.3.0'
    implementation("com.android.billingclient:billing:7.0.0")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation 'org.webkit:android-jsc:+'
    }
}