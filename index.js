import {AppRegistry} from 'react-native';
import App from './App';
import appJson from './app.json';
import {ThemeProvider} from './src/components/ThemeContext';
import New from './New';
import {Environment} from './src/config/environment';

// Dynamically select appName based on environment (for multi-variant support)
const appName = Environment.APP_NAME || appJson.name;

console.log('=== React Native App Registration ===');
console.log('Registering component with name:', appName);
console.log('Environment APP_NAME:', Environment.APP_NAME);
console.log('Fallback app.json name:', appJson.name);

export default function Main() {
  return (
    // <ThemeProvider>
    <New />
    // </ThemeProvider>
  );
}

AppRegistry.registerComponent(appName, () => Main);
