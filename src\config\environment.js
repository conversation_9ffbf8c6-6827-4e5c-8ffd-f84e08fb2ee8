/**
 * Environment Configuration
 * 
 * This file handles dynamic environment variable loading based on the app variant.
 * The environment variables are loaded at build time through babel-plugin-react-native-dotenv
 * based on the APP_VARIANT and APP_ENV environment variables.
 */

import {
  APP_NAME,
  APP_ID,
  EXAM_TYPE,
  WP_API_URL,
  NODE_API_URL,
  API_TOKEN,
  CACHE_TTL,
  REVENUECAT_API_KEY,
  REVENUECAT_REST_API_KEY,
  REVENU<PERSON>AT_PROJECT_ID,
  ADMOB_APP_ID,
  CREDIT_VIA_AD,
  CREDIT_VIA_PURCHASE,
  ENABLE_REALTIME_SYNC,
  USER_PROGRESS_SYNC_WINDOW,
  AI_CHAT_SYNC_WINDOW,
  RE<PERSON>NUECAT_AI_CREDIT_WEEKLY,
  REVENUECAT_AI_CREDIT_MONTHLY,
  REVENUECAT_AI_CREDIT_BIMONTHLY,
  IS_UNLOCKED,
  IS_FOR_TESTER
} from '@env';

// Debug logging for environment variables
console.log('=== Environment Variables Debug ===');
console.log('IS_FOR_TESTER (raw):', IS_FOR_TESTER);
console.log('IS_UNLOCKED (raw):', IS_UNLOCKED);
console.log('APP_NAME (raw):', APP_NAME);
console.log('APP_NAME (effective):', IS_FOR_TESTER === 'true' ? 'QA' : APP_NAME);
console.log('APP_ID:', APP_ID);
console.log('EXAM_TYPE:', EXAM_TYPE);

/**
 * Get the effective app name based on IS_FOR_TESTER flag
 * This matches the logic in android/app/build.gradle
 */
const getEffectiveAppName = () => {
  if (IS_FOR_TESTER === 'true') {
    return 'QA';
  }
  return APP_NAME;
};

/**
 * Environment configuration object
 * All environment variables are loaded from the appropriate .env file
 * based on the build variant and environment
 */
export const Environment = {
  // App Configuration
  APP_NAME: getEffectiveAppName(),
  APP_ID,
  EXAM_TYPE,
  
  // API Configuration
  WP_API_URL,
  NODE_API_URL,
  API_TOKEN,
  CACHE_TTL: parseInt(CACHE_TTL, 10),
  
  // RevenueCat Configuration
  REVENUECAT_API_KEY,
  REVENUECAT_REST_API_KEY,
  REVENUECAT_PROJECT_ID,
  
  // AdMob Configuration
  ADMOB_APP_ID,
  
  // Credit Configuration
  CREDIT_VIA_AD: parseInt(CREDIT_VIA_AD, 10),
  CREDIT_VIA_PURCHASE: parseInt(CREDIT_VIA_PURCHASE, 10),
  
  // Real-time Sync Configuration
  ENABLE_REALTIME_SYNC: ENABLE_REALTIME_SYNC === 'true',
  USER_PROGRESS_SYNC_WINDOW: parseInt(USER_PROGRESS_SYNC_WINDOW, 10),
  AI_CHAT_SYNC_WINDOW: parseInt(AI_CHAT_SYNC_WINDOW, 10),
  
  // AI Credit Configuration
  REVENUECAT_AI_CREDIT_WEEKLY: parseInt(REVENUECAT_AI_CREDIT_WEEKLY, 10),
  REVENUECAT_AI_CREDIT_MONTHLY: parseInt(REVENUECAT_AI_CREDIT_MONTHLY, 10),
  REVENUECAT_AI_CREDIT_BIMONTHLY: parseInt(REVENUECAT_AI_CREDIT_BIMONTHLY, 10),
  
  // Development Configuration
  IS_UNLOCKED: IS_UNLOCKED === 'true',
  IS_FOR_TESTER: IS_FOR_TESTER === 'true',
  
  // Helper methods
  isAWS: () => EXAM_TYPE === 'AWS',
  isPMI: () => EXAM_TYPE === 'PMI',
  isDevelopment: () => APP_ID.includes('.dev'),
  isProduction: () => !APP_ID.includes('.dev'),
};

/**
 * Get the current app variant
 * @returns {string} 'aws' or 'pmi'
 */
export const getAppVariant = () => {
  return EXAM_TYPE?.toLowerCase() || 'aws';
};

/**
 * Get the current environment
 * @returns {string} 'dev' or 'release'
 */
export const getEnvironment = () => {
  return Environment.isDevelopment() ? 'dev' : 'release';
};

/**
 * Log current environment configuration (for debugging)
 */
export const logEnvironmentConfig = () => {
  if (__DEV__) {
    console.log('=== Environment Configuration ===');
    console.log('App Name:', Environment.APP_NAME);
    console.log('App ID:', Environment.APP_ID);
    console.log('Exam Type:', Environment.EXAM_TYPE);
    console.log('Environment:', getEnvironment());
    console.log('Variant:', getAppVariant());
    console.log('API URL:', Environment.NODE_API_URL);
    console.log('AdMob App ID:', Environment.ADMOB_APP_ID);
    console.log('Real-time Sync Enabled:', Environment.ENABLE_REALTIME_SYNC);
    console.log('================================');
  }
};

export default Environment;
