import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const QuizResultContext = createContext();

export const QuizResultProvider = ({ children }) => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);

  // Load results from AsyncStorage on app start
  useEffect(() => {
    loadResults();
  }, []);

  const loadResults = async () => {
    try {
      setLoading(true);
      const storedResults = await AsyncStorage.getItem('quiz_results');
      if (storedResults) {
        const parsedResults = JSON.parse(storedResults);
        setResults(Array.isArray(parsedResults) ? parsedResults : []);
      }
    } catch (error) {
      console.error('Error loading quiz results:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const saveResults = async (newResults) => {
    try {
      await AsyncStorage.setItem('quiz_results', JSON.stringify(newResults));
    } catch (error) {
      console.error('Error saving quiz results:', error);
      throw error;
    }
  };

  const addResult = async (resultData) => {
    try {
      const newResults = [resultData, ...results];
      setResults(newResults);
      await saveResults(newResults);
      console.log('[QuizResultContext] Result added successfully');
    } catch (error) {
      console.error('[QuizResultContext] Error adding result:', error);
      throw error;
    }
  };

  const updateResult = async (resultId, updatedData) => {
    try {
      const newResults = results.map(result => 
        result.id === resultId ? { ...result, ...updatedData } : result
      );
      setResults(newResults);
      await saveResults(newResults);
      console.log('[QuizResultContext] Result updated successfully');
    } catch (error) {
      console.error('[QuizResultContext] Error updating result:', error);
      throw error;
    }
  };

  const deleteResult = async (resultId) => {
    try {
      const newResults = results.filter(result => result.id !== resultId);
      setResults(newResults);
      await saveResults(newResults);
      console.log('[QuizResultContext] Result deleted successfully');
    } catch (error) {
      console.error('[QuizResultContext] Error deleting result:', error);
      throw error;
    }
  };

  const clearAllResults = async () => {
    try {
      setResults([]);
      await AsyncStorage.removeItem('quiz_results');
      console.log('[QuizResultContext] All results cleared successfully');
    } catch (error) {
      console.error('[QuizResultContext] Error clearing all results:', error);
      throw error;
    }
  };

  const getResultsByExam = (examCode) => {
    return results.filter(result => result.examCode === examCode);
  };

  const getResultById = (resultId) => {
    return results.find(result => result.id === resultId);
  };

  const getLatestResult = (examCode = null) => {
    const filteredResults = examCode 
      ? results.filter(result => result.examCode === examCode)
      : results;
    
    return filteredResults.length > 0 ? filteredResults[0] : null;
  };

  const getResultsStats = (examCode = null) => {
    const filteredResults = examCode 
      ? results.filter(result => result.examCode === examCode)
      : results;

    if (filteredResults.length === 0) {
      return {
        totalQuizzes: 0,
        averageScore: 0,
        highestScore: 0,
        lowestScore: 0,
        totalTimeSpent: 0
      };
    }

    const scores = filteredResults.map(result => result.score);
    const totalTimeSpent = filteredResults.reduce((sum, result) => sum + (result.timeSpent || 0), 0);

    return {
      totalQuizzes: filteredResults.length,
      averageScore: scores.reduce((sum, score) => sum + score, 0) / scores.length,
      highestScore: Math.max(...scores),
      lowestScore: Math.min(...scores),
      totalTimeSpent
    };
  };

  const value = {
    results,
    loading,
    addResult,
    updateResult,
    deleteResult,
    clearAllResults, // Make sure this is included
    getResultsByExam,
    getResultById,
    getLatestResult,
    getResultsStats,
    loadResults
  };

  return (
    <QuizResultContext.Provider value={value}>
      {children}
    </QuizResultContext.Provider>
  );
};

export const useQuizResult = () => {
  const context = useContext(QuizResultContext);
  if (!context) {
    throw new Error('useQuizResult must be used within a QuizResultProvider');
  }
  return context;
};

export default QuizResultContext;