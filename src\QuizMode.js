import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import {
  BackHandler,
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  useWindowDimensions,
  Alert,
  ScrollView,
  Image,
  Modal
} from 'react-native';

import {
  Text, IconButton, useTheme, ProgressBar, Button,
  Appbar, Card
} from 'react-native-paper';

import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FastImage from 'react-native-fast-image';
import { PinchGestureHandler, State } from 'react-native-gesture-handler';
import ScaledImage from './components/ScaledImage';
import apiClient from './services/ApiClient';
import { useQuizResult } from './store/QuizResultContext';
import { useQnAContext } from './store/QnAContext';
import { useUserProgress } from './store/UserProgressContext';
import { useQuizSetting } from './store/QuizSettingContext';
import { useExamContext } from './store/ExamContext';
import { shuffleArray } from './utils/shuffleArray';
import { shuffleAndReorderChoices } from './utils/shuffleAndReorderChoices';
import { usePreferencesContext } from './store/PreferencesContext';
import FixedFontButton from './components/FixedFontButton';
import CustomAppbarContent from './components/CustomAppbarContent';

const QuizMode = ({ navigation, route }) => {
  // ALL HOOKS MUST BE CALLED FIRST - NO CONDITIONAL LOGIC BEFORE THIS POINT
  
  // Basic hooks
  const { shouldShuffleChoices } = usePreferencesContext();
  const { colors } = useTheme();
  const { questionsBySubject } = useQnAContext();
  const {
    progress,
    removeFromIncorrect,
    markIncorrect,
    batchRemoveFromIncorrect,
    batchMarkIncorrect,
    toggleBookmark,
    isBookmarked
  } = useUserProgress();
  const { appliedFilters } = useQuizSetting();
  const { selectedExam } = useExamContext();
  const { results, addResult } = useQuizResult();
  const { width: screenWidth } = useWindowDimensions();

  // All useState hooks
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [timeSpent, setTimeSpent] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [selectedImageUri, setSelectedImageUri] = useState(null);
  const [scale, setScale] = useState(1);
  const [dimensions, setDimensions] = useState({
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height
  });
  const [quizSessionId, setQuizSessionId] = useState(0);
  const [bookmarkedStatuses, setBookmarkedStatuses] = useState({});

  // All useRef hooks
  const initialProgressRef = useRef(progress);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const panelHeight = useRef(new Animated.Value(80)).current;
  const scrollViewRef = useRef(null);

  // Route params - this is safe as it doesn't change during component lifecycle
  const { retakeQuestions, resultData } = route.params || {};
  
  // Initialize flaggedIds state
  const [flaggedIds, setFlaggedIds] = useState(() => {
    if (retakeQuestions && resultData?.flaggedIds) {
      return new Set(resultData.flaggedIds.map(id => String(id)));
    }
    return new Set();
  });

  // All useMemo hooks - these must be called consistently
  const stableQuestionsBySubject = useMemo(() => questionsBySubject, [questionsBySubject]);
  
  const stableSelectedExam = useMemo(() => selectedExam, [
    selectedExam?.id, 
    selectedExam?.subjects, 
    selectedExam?.exam_code
  ]);
  
  const stableAppliedFilters = useMemo(() => appliedFilters, [
    appliedFilters?.subjects,
    appliedFilters?.dateRange?.startDate,
    appliedFilters?.dateRange?.endDate,
    appliedFilters?.questionLimit,
    appliedFilters?.onlyNotQuized,
    appliedFilters?.onlyIncorrect,
    appliedFilters?.onlyFlagged,
    appliedFilters?.onlyBookmarked,
    appliedFilters?.onlyUnbrowsed
  ]);

  const rawQuestions = useMemo(() => {
    if (!stableQuestionsBySubject || !stableSelectedExam) return [];
    
    const examSubjects = stableSelectedExam?.subjects || [];

    console.log('selectedExam: ', stableSelectedExam)
    console.log(`progress: `, progress);
    return Object.values(stableQuestionsBySubject)
      .flat()
      .map((question, originalIndex) => {
        // Handle subject parsing with proper null checks
        let subjectNumber = 1; // Default to subject 1
        if (question.subject && typeof question.subject === 'string') {
          const parsed = parseInt(question.subject.split('.')[0]?.trim() || '1', 10);
          subjectNumber = isNaN(parsed) ? 1 : parsed;
        } else if (question.subject && typeof question.subject === 'number') {
          subjectNumber = question.subject;
        }
        const bookmarked = initialProgressRef.current[stableSelectedExam?.id]?.[subjectNumber]?.bookmarked?.find(b => b.id === question._id)?.timestamp || 0;
        const last_incorrect = initialProgressRef.current[stableSelectedExam?.id]?.[subjectNumber]?.incorrect.find(b => b.id === question._id)?.timestamp || 0;
        const last_browsed = initialProgressRef.current[stableSelectedExam?.id]?.[subjectNumber]?.browsed.find(b => b.id === question._id)?.timestamp || 0;

        return {
          ...question,
          originalIndex,
          id: question._id,
          subject: subjectNumber,
          subjectName: examSubjects[subjectNumber - 1],
          bookmarked,
          last_incorrect,
          last_browsed,
          created: question.createdAt || 0,
          question: question.exam.question || '',
          answer: Array.isArray(question.exam.answer)
            ? question.exam.answer
            : (question.exam.answer ? [question.exam.answer] : []),
          choices: __DEV__ 
            ? question.exam.choices || []
            : question.exam.choices?.map(choice => {
                const [key, value] = Object.entries(choice)[0];
                return { [key]: value.replace(' (correct answer)', '').trim() };
              }) || [],
          ai_explanation: question.ai_explanation || question.exam.ai_explanation ||'',
        };
      });
  }, [stableQuestionsBySubject, stableSelectedExam, quizSessionId]);

  // Create a set of previously quized question IDs for the current exam
  const previouslyQuizedQuestionIds = useMemo(() => {
    if (!stableAppliedFilters?.onlyNotQuized) return new Set();
    
    const quizedIds = new Set();
    
    // Get all results for the current exam
    const examResults = results.filter(result => result.examCode === stableSelectedExam?.exam_code);
    
    // Extract all question IDs from previous quiz results
    examResults.forEach(result => {
      if (result.questions && Array.isArray(result.questions)) {
        result.questions.forEach(question => {
          if (question.id) {
            quizedIds.add(question.id);
          }
        });
      }
    });
    
    console.log('Previously quized question IDs:', Array.from(quizedIds));
    return quizedIds;
  }, [results, stableSelectedExam?.exam_code, stableAppliedFilters?.onlyNotQuized]);

  const initialQuestions = useMemo(() => {
    if (retakeQuestions) {
      return retakeQuestions.map(q => {
        if (shouldShuffleChoices) {
          const { shuffledChoices, mappedAnswer } =
            shuffleAndReorderChoices(q.choices, q.answer);
          return {
            ...q,
            answer: mappedAnswer,  // Update answers to new indices
            choices: shuffledChoices
          };
        } else {
          // Preserve original answer order explicitly
          return {
            ...q,
            answer: q.answer
          };
        }
      });
    }

    if (!rawQuestions.length) return [];

    const filtered = rawQuestions.filter(q => {
      // Subject filter
      if (!stableAppliedFilters?.subjects?.includes(q.subject)) {
        return false;
      }

      // Date filter
      if (stableAppliedFilters?.dateRange?.startDate && stableAppliedFilters?.dateRange?.endDate) {
        const qDate = new Date(q.created * 1000);
        if (!(qDate >= stableAppliedFilters.dateRange.startDate && qDate <= stableAppliedFilters.dateRange.endDate)) {
          return false;
        }
      }

      // Quiz filters
      const { onlyIncorrect, onlyBookmarked, onlyFlagged, onlyUnbrowsed, onlyNotQuized } = stableAppliedFilters || {};
      const anyQuizFilterEnabled = onlyIncorrect || onlyBookmarked || onlyFlagged || onlyUnbrowsed || onlyNotQuized;

      if (anyQuizFilterEnabled) {
        const meetsCondition =
          (onlyIncorrect && q.last_incorrect) ||
          (onlyBookmarked && q.bookmarked) ||
          (onlyFlagged && flaggedIds.has(q.id)) ||
          (onlyUnbrowsed && !q.last_browsed) ||
          (onlyNotQuized && !previouslyQuizedQuestionIds.has(q.id));

        if (!meetsCondition) {
          return false;
        }
      }

      return true;
    });

    console.log('rawQuestions: ', rawQuestions)
    console.log('Filtered Questions:', filtered);

    let limitedQuestions = shuffleArray(filtered);

    if (stableAppliedFilters?.questionLimit && stableAppliedFilters.questionLimit !== 'All') {
      limitedQuestions = limitedQuestions.slice(0, Number(stableAppliedFilters.questionLimit));
    }
    console.log('limitedQuestions:', limitedQuestions);

    return limitedQuestions.map(q => {
      if (shouldShuffleChoices) {
        const { shuffledChoices, mappedAnswer } =
          shuffleAndReorderChoices(q.choices, q.answer);
        return { ...q, answer: mappedAnswer, choices: shuffledChoices };
      }
      return q;
    });
  }, [rawQuestions, stableAppliedFilters, retakeQuestions, shouldShuffleChoices, quizSessionId, previouslyQuizedQuestionIds, flaggedIds]);

  // Update the questions reference to use initialQuestions
  const questions = useMemo(() =>
    initialQuestions.map(q => ({
      ...q,
      isFlagged: flaggedIds.has(q.originalIndex),
      isBookmarked: !!bookmarkedStatuses[q.id],
    })),
    [initialQuestions, flaggedIds, bookmarkedStatuses]
  );

  // Calculate pagination values
  const horizontalPadding = 48;
  const itemWidth = 40;
  const gap = 6;
  const availableWidth = screenWidth - horizontalPadding;
  const itemsPerRow = Math.floor((availableWidth + gap) / (itemWidth + gap));
  const itemsPerPage = itemsPerRow * 3; // 3 rows
  const totalPages = Math.ceil(questions.length / itemsPerPage);

  // ALL useEffect hooks - these must be called consistently
  useEffect(() => {
    if (!retakeQuestions) {
      setQuizSessionId(prev => prev + 1);
      setFlaggedIds(new Set()); // Reset flags for new sessions
    }
  }, [retakeQuestions]);

  useEffect(() => {
    initialProgressRef.current = progress;
  }, [progress, quizSessionId]);

  // Initialize bookmarks
  useEffect(() => {
    const initial = {};
    rawQuestions.forEach(q => {
      initial[q.id] = q.bookmarked;
    });
    setBookmarkedStatuses(initial);
    console.log('rawQuestions: ', rawQuestions)
  }, [rawQuestions]);

  // Set initialization complete after questions are processed
  useEffect(() => {
    if (rawQuestions.length > 0 || stableQuestionsBySubject) {
      const timer = setTimeout(() => {
        setIsInitializing(false);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [rawQuestions.length, stableQuestionsBySubject]);

  // Reset current question when questions change
  useEffect(() => {
    if (questions.length > 0) {
      setCurrentQuestion(0);
    }
  }, [questions.length]); // Use questions.length instead of questions to prevent unnecessary resets

  // Modified alert effect with initialization check and submission check
  useEffect(() => {
    // Don't show alert if we're submitting or if it's a retake quiz
    if (!isInitializing && !isSubmitting && !retakeQuestions && initialQuestions.length === 0 && rawQuestions.length > 0) {
      Alert.alert(
        'No Questions Available',
        'No questions match your current filter settings. Please try adjusting your filters:\n\n• Select different subjects\n• Disable specific filters (incorrect, bookmarked, etc.)',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    }
    console.log('initialQuestions: ', initialQuestions)
  }, [initialQuestions.length, navigation, isInitializing, rawQuestions.length, isSubmitting, retakeQuestions]);

  // Dimensions change handler
  const handleDimensionsChange = useCallback(({ window }) => {
    setDimensions({
      width: window.width,
      height: window.height
    });
  }, []);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', handleDimensionsChange);
    return () => subscription?.remove();
  }, [handleDimensionsChange]);

  const handleBackPress = useCallback(() => {
    Alert.alert(
      'Exit Quiz',
      'Are you sure you want to leave? Progress will be lost!',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Exit',
          onPress: () => navigation.reset({
            index: 0,
            routes: [{ name: 'Quiz' }],
          })
        }
      ]
    );
    return true;
  }, [navigation]);

  useEffect(() => {
    const subscription = BackHandler.addEventListener(
      'hardwareBackPress',
      handleBackPress
    );
    return () => subscription.remove();
  }, [handleBackPress]);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(prev => prev + 1);
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        Alert.alert(
          'Exit Quiz',
          'Are you sure you want to leave? Progress will be lost!',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Exit', onPress: () => navigation.goBack() }
          ]
        );
        return true;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => subscription.remove();
    }, [navigation])
  );

  useEffect(() => {
    const newPage = Math.floor(currentQuestion / itemsPerPage);
    setCurrentPage(newPage);
  }, [currentQuestion, itemsPerPage]);

  // ALL useCallback hooks
  // Function to scroll to top of question content
  const scrollToQuestionTop = useCallback(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        y: 0,
        animated: false // Changed to false for instant scroll
      });
    }
  }, []);

  const animateTransition = useCallback((newIndex) => {
    setIsTransitioning(true);
    
    // Fade out only
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 120,
      useNativeDriver: true,
    }).start(() => {
      // Scroll and change question during fade out
      scrollToQuestionTop();
      setCurrentQuestion(newIndex);
      
      // Immediately show new content (no fade in)
      fadeAnim.setValue(1);
      setIsTransitioning(false);
    });
  }, [fadeAnim, scrollToQuestionTop]);

  // Add question navigation handler
  const handleQuestionNavigate = useCallback((index) => {
    if (index !== currentQuestion) {
      animateTransition(index);
    }
  }, [currentQuestion, animateTransition]);

  const handleBookmarkToggle = useCallback(() => {
    const currentQ = questions[currentQuestion];
    if (!currentQ?.id) return;

    toggleBookmark(stableSelectedExam.id, currentQ.subject, currentQ.id);
    setBookmarkedStatuses(prev => ({
      ...prev,
      [currentQ.id]: !prev[currentQ.id],
    }));
  }, [currentQuestion, questions, stableSelectedExam, toggleBookmark]);

  const toggleFlag = useCallback(() => {
    const questionId = questions[currentQuestion]?.originalIndex;
    if (questionId === undefined) return;
    setFlaggedIds(prev => {
      const newSet = new Set(prev);
      newSet.has(questionId) ? newSet.delete(questionId) : newSet.add(questionId);
      return newSet;
    });
  }, [currentQuestion, questions]);

  const handlePrevious = useCallback(() => {
    const newIndex = Math.max(0, currentQuestion - 1);
    if (newIndex !== currentQuestion) {
      animateTransition(newIndex);
    }
  }, [currentQuestion, animateTransition]);

  const handleNext = useCallback(() => {
    const newIndex = Math.min(questions.length - 1, currentQuestion + 1);
    if (newIndex !== currentQuestion) {
      animateTransition(newIndex);
    }
  }, [currentQuestion, questions.length, animateTransition]);

  // NOW we can do conditional returns after ALL hooks are called
  if (isInitializing || questions.length === 0) {
    return null; // Or a loading indicator if needed
  }

  // Add status color calculation
  const getQuestionStatus = (index) => {
    if (index === currentQuestion) return 'current';
    if (selectedAnswers[index]?.length > 0) return 'answered';
    return 'unanswered';
  };

  const questionStatusColors = {
    current: colors.errorContainer,
    answered: colors.primaryContainer,
    unanswered: colors.surface,
  };

  const statusTextColors = {
    current: colors.onErrorContainer,
    answered: colors.onPrimaryContainer,
    unanswered: colors.onSurfaceVariant,
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (choiceKey) => {
    const currentQuestionData = questions[currentQuestion];
    const isMultiple = currentQuestionData.answer.length > 1;

    setSelectedAnswers(prev => {
      const currentAnswers = prev[currentQuestion] || [];

      return {
        ...prev,
        [currentQuestion]: isMultiple
          ? currentAnswers.includes(choiceKey)
            ? currentAnswers.filter(k => k !== choiceKey)
            : [...currentAnswers, choiceKey]
          : [choiceKey] // Single answer replacement
      };
    });
  };

  const handleQuizComplete = async (correctAnswers, totalQuestions) => {
    const score = (correctAnswers / totalQuestions) * 100;
    try {
      await addResult({
        score,
        correctAnswers,
        totalQuestions,
      });
    } catch (error) {
      console.error('Error adding quiz result:', error);
    }
    navigation.navigate('QuizResult', {
      score,
      correctAnswers,
      totalQuestions
    });
  };

  const handleSubmit = () => {
    if (isSubmitting) return; // Prevent double submission

    const answeredCount = Object.keys(selectedAnswers).length;

    if (answeredCount < questions.length) {
      Alert.alert(
        'Submit Quiz?',
        `You've answered ${answeredCount} of ${questions.length} questions.\nIf you submit, you'll only be scored on the answered questions.`,
        [
          { text: 'Continue Quiz', style: 'cancel' },
          {
            text: 'Submit Quiz',
            onPress: async () => {
              setIsSubmitting(true);
              setTimeout(async () => {  // Add setTimeout
                await navigateToResult();
              }, 0);
            }
          }
        ]
      );
    } else {
      setIsSubmitting(true);
      setTimeout(async () => {  // Add setTimeout
        await navigateToResult();
      }, 0);
    }
  };

  const navigateToResult = async () => {
    try {
      // Optimized correct answer calculation
      let correctCount = 0;
      const examId = stableSelectedExam.id;

      // Create maps to group questions by subject
      const correctBySubject = {};
      const incorrectBySubject = {};

      // Process all questions and group them by subject
      console.log('Processing questions for batch update...');
      for (let i = 0; i < questions.length; i++) {
        const q = questions[i];
        const userAnswers = selectedAnswers[i] || [];
        const correctAnswers = q.answer;
        const subjectNumber = q.subject;

        // Initialize subject arrays if they don't exist
        if (!correctBySubject[subjectNumber]) correctBySubject[subjectNumber] = [];
        if (!incorrectBySubject[subjectNumber]) incorrectBySubject[subjectNumber] = [];

        // Optimized validation using Set for multiple answers
        let isCorrect;
        if (correctAnswers.length === 1) {
          isCorrect = userAnswers[0] === correctAnswers[0];
        } else {
          const answerSet = new Set(correctAnswers);
          const userSet = new Set(userAnswers);
          isCorrect = userSet.size === answerSet.size &&
            [...userSet].every(a => answerSet.has(a));
        }

        // Add to the appropriate subject group
        if (isCorrect) {
          correctCount++;
          correctBySubject[subjectNumber].push(q.id);
        } else {
          incorrectBySubject[subjectNumber].push(q.id);
        }
      }

      // Filter out empty subject arrays
      Object.keys(correctBySubject).forEach(subject => {
        if (correctBySubject[subject].length === 0) {
          delete correctBySubject[subject];
        }
      });

      Object.keys(incorrectBySubject).forEach(subject => {
        if (incorrectBySubject[subject].length === 0) {
          delete incorrectBySubject[subject];
        }
      });

      // Batch update progress
      if (Object.keys(correctBySubject).length > 0) {
        console.log('Batch updating correct answers:', correctBySubject);
        batchRemoveFromIncorrect(examId, correctBySubject);
      }

      if (Object.keys(incorrectBySubject).length > 0) {
        console.log('Batch updating incorrect answers:', incorrectBySubject);
        batchMarkIncorrect(examId, incorrectBySubject);
      }

      const totalQuestions = questions.length;
      const score = (correctCount / totalQuestions) * 100;

      const resultData = {
        id: `result-${Date.now()}`,
        examCode: stableSelectedExam?.exam_code,
        score: Number(score.toFixed(1)),
        correctAnswers: correctCount,
        totalQuestions,
        date: new Date().toISOString(),
        timeSpent,
        flaggedIds: Array.from(flaggedIds),
        answers: questions.reduce((acc, q, index) => {
          const originalIndex = q.originalIndex;
          acc[originalIndex] = selectedAnswers[index] || [];
          return acc;
        }, {}),
        questions
      };

      try {
        await addResult(resultData);
        console.log('[QuizMode] Quiz result added and synced successfully');
      } catch (resultError) {
        console.error('[QuizMode] Error adding quiz result:', resultError);
        // Continue with navigation even if result sync fails
      }

      // Log the results of the batch operations
      console.log('Quiz completed with score:', score.toFixed(1) + '%');
      console.log('Correct answers:', correctCount, 'out of', totalQuestions);

      navigation.replace('QuizResult', {
        resultId: resultData.id,
        resultData,
        examCode: stableSelectedExam.exam_code
      });
    } catch (error) {
      setIsSubmitting(false);
      console.error('Submission error:', error);
    }
  };

  const NavigationGrid = () => (
    <ScrollView contentContainerStyle={styles.gridContainer}>
      {questions.map((_, index) => {
        const questionStatus = getQuestionStatus(index);
        return (
          <Button
            key={index}
            style={[
              styles.gridButton,
              {
                // Use the correct variable name here
                backgroundColor: questionStatusColors[questionStatus], // Fixed variable name
                borderColor: colors.outline,
              }
            ]}
            labelStyle={styles.gridLabel}
            onPress={() => handleQuestionNavigate(index)}
          >
            {index + 1}
          </Button>
        );
      })}
    </ScrollView>
  );

  const handleImagePress = (uri) => {
    if (!uri) {
      console.warn('Attempted to open image modal with null URI in QuizMode');
      return;
    }
    setSelectedImageUri(uri);
    setIsImageModalVisible(true);
    setScale(1); // Reset scale when opening
  };

  const handlePinchGesture = (event) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      setScale(event.nativeEvent.scale);
    }
  };

  // Function to render HTML content with images
  const renderHtmlContent = (htmlContent, isChoice) => {
    if (!htmlContent) return null;

    const parts = htmlContent.split(/(<img[^>]+>)/g);

    return parts.map((part, index) => {
      if (part.startsWith('<img')) {
        const srcMatch = part.match(/src="([^"]+)"/);
        if (!srcMatch) return null;
        let src = srcMatch[1]
          .split('/')
          .map(segment => encodeURIComponent(segment))
          .join('%2F');

        src = apiClient.baseUrl + '/image/' + src;

        return (
          <TouchableOpacity
            key={`img-${index}`}
            onPress={() => handleImagePress(src)}
            style={{ flexDirection: 'row' }}
          >
            <ScaledImage
              uri={src}
              width={isChoice ? (dimensions.width - 105) : dimensions.width - 45}
            />
          </TouchableOpacity>
        );
      } else {
        const isAfterImage = index > 0 && parts[index - 1].startsWith('<img');
        const isBeforeImage = index < parts.length - 1 && parts[index + 1].startsWith('<img');
        let processedPart = part;

        if (isAfterImage) {
          processedPart = processedPart
            .replace(/^(\s*<br\s*\/?>\s*)+/gi, '')
            .replace(/^\n+/g, '');
        }

        if (isBeforeImage) {
          processedPart = processedPart
            .replace(/(\s*<br\s*\/?>\s*)+$/gi, '')
            .replace(/\n+$/g, '');
        }

        if (processedPart.trim() === '') return null;

        const textParts = processedPart.split(/<br\s*\/?>/gi);

        return textParts.map((textSegment, textIndex) => (
          <Text
            key={`text-${index}-${textIndex}`}
            style={{
              color: colors.onSurface,
              fontSize: 16,
              lineHeight: 24,
              textAlign: isChoice ? 'left' : 'justify'
            }}
          >
            {textSegment.replace(/<\/?[^>]+(>|$)/g, '')}
          </Text>
        ));
      }
    });
  };

  const currentQ = questions[currentQuestion];
  const isCurrentlyBookmarked = isBookmarked(stableSelectedExam?.id, currentQ.subject, currentQ.id);
  
  return (
    <View style={{ flex: 1, backgroundColor: colors.background }}>

      <Appbar.Header elevated>
        <Appbar.BackAction onPress={handleBackPress} />
        <CustomAppbarContent title={
          <Text maxFontSizeMultiplier={1.8}>
            Quiz Mode
          </Text>
        } />
        <View style={{ paddingRight: 10 }}>
          <Text style={[styles.counter, { color: colors.onSurface }]} maxFontSizeMultiplier={1.5}>
            Q.{currentQuestion + 1} of {questions.length}
          </Text>
        </View>
      </Appbar.Header>

      {/* Add Status Panel */}
      <Card style={[styles.panel, { margin: 8 }]}>
        <TouchableOpacity
          onPress={() => setIsExpanded(!isExpanded)}
          activeOpacity={0.8}>
          <Card.Title
            title="Question Navigator"
            titleStyle={{
              color: colors.primary,
              fontSize: 14,
              marginLeft: -8,
              lineHeight: 30
            }}
            style={{
              paddingVertical: 2, // Reduced from 4
              alignItems: 'center',
              minHeight: 40 // Added min height constraint
            }}
            left={(props) => (
              <MaterialCommunityIcons
                {...props}
                name={isExpanded ? "chevron-up" : "chevron-down"}
                size={24}
                color={colors.primary}
                style={{
                  marginLeft: 8,
                  marginRight: 4,
                  marginVertical: 0 // Removed vertical margin
                }}
              />
            )}
          />
        </TouchableOpacity>

        {isExpanded ? (
          <Card.Content>
            <View style={styles.paginationContainer}>
              {currentPage > 0 ? (
                <IconButton
                  icon="chevron-left"
                  size={20}
                  onPress={() => setCurrentPage(p => p - 1)}
                  style={styles.paginationButton}
                />
              ) : null}
              <View style={styles.grid}>
                {questions
                  .slice(currentPage * itemsPerPage, (currentPage + 1) * itemsPerPage)
                  .map((question, index) => {
                    const absoluteIndex = currentPage * itemsPerPage + index;
                    const status = getQuestionStatus(absoluteIndex);

                    return (
                      <TouchableOpacity
                        key={question.id}
                        style={[
                          styles.gridItem,
                          {
                            backgroundColor: questionStatusColors[status],
                            borderColor: colors.outline,
                          }
                        ]}
                        onPress={() => handleQuestionNavigate(absoluteIndex)}
                      >
                        {/* Use originalIndex for flagged check */}
                        {flaggedIds.has(question.originalIndex) ? (
                          <MaterialCommunityIcons
                            name="flag"
                            size={12}
                            style={styles.flagIcon}
                            color={colors.error}
                          />
                        ) : null}
                        <Text style={[styles.gridText, { color: statusTextColors[status] }]}>
                          {absoluteIndex + 1}
                        </Text>
                        {/* Bookmarked check remains using question.id */}
                        {bookmarkedStatuses[question.id] ? (
                          <MaterialCommunityIcons
                            name="bookmark"
                            size={12}
                            style={styles.bookmarkIcon}
                            color={colors.primary}
                          />
                        ) : null}
                      </TouchableOpacity>
                    );
                  })}
              </View>

              {currentPage < totalPages - 1 ? (
                <IconButton
                  icon="chevron-right"
                  size={20}
                  onPress={() => setCurrentPage(p => p + 1)}
                  style={styles.paginationButton}
                />
              ) : null}
            </View>
          </Card.Content>
        ) : null}
      </Card>

      {/* Question Content */}
      <ScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View style={{ opacity: fadeAnim }}>
          <View>
            {renderHtmlContent(questions[currentQuestion]?.question || '', false)}
          </View>

          <Text style={[styles.questionType, { color: colors.onSurfaceVariant, marginTop: 16 }]}>
            {(questions[currentQuestion]?.answer?.length || 0) > 1
              ? "Select all that apply"
              : "Choose the correct answer"}
          </Text>

          <View style={styles.choicesContainer}>
            {questions[currentQuestion].choices.map((choiceObj) => {
            const [key, value] = Object.entries(choiceObj)[0];
            const isSelected = selectedAnswers[currentQuestion]?.includes(key);
            const isCorrectAnswer = questions[currentQuestion].answer.includes(key);
            const showValidation = showResults; // Add this state variable if needed

            return (
              <TouchableOpacity
                key={key}
                style={[
                  styles.choiceButton,
                  {
                    borderColor: isSelected ? colors.primary : colors.outline,
                    backgroundColor: isSelected
                      ? colors.primaryContainer
                      : colors.surface,
                    // For post-submission validation
                    ...(showValidation && {
                      backgroundColor: isCorrectAnswer
                        ? colors.successContainer
                        : isSelected
                          ? colors.errorContainer
                          : colors.surface
                    })
                  }
                ]}
                onPress={() => handleAnswerSelect(key)}
              >
                <View style={{ flexDirection: 'row', alignItems: 'flex-start' }}>
                  <Text style={{
                    fontSize: 16,
                    fontWeight: '500',
                    marginRight: 8,
                    lineHeight: 24,
                    color: colors.onSurface
                  }}>
                    {key}.
                  </Text>
                  <View style={{ flex: 1 }}>
                    {renderHtmlContent(value, true)}
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
          </View>
        </Animated.View>
      </ScrollView>

      {/* Image Modal */}
      <Modal
        visible={isImageModalVisible}
        transparent={true}
        onRequestClose={() => setIsImageModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <PinchGestureHandler onGestureEvent={handlePinchGesture}>
            <FastImage
              style={[
                styles.fullImage,
                {
                  width: dimensions.width,
                  height: dimensions.height / 2,
                  transform: [{ scale }]
                },
              ]}
              source={selectedImageUri ? { uri: selectedImageUri } : null}
              resizeMode={FastImage.resizeMode.contain}
              onError={() => {
                console.log('Error loading image in QuizMode modal:', selectedImageUri);
                setIsImageModalVisible(false);
              }}
            />
          </PinchGestureHandler>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setIsImageModalVisible(false)}
          >
            <Text style={{ color: 'white', fontSize: 18 }}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>

      <View style={[styles.bottomBar, { borderColor: colors.surfaceVariant }]}>
        <FixedFontButton
          mode="contained"
          onPress={handleBackPress}
          style={styles.submitButton}
          labelStyle={{ color: '#FFFFFF', fontWeight: '600' }}
          disabled={isSubmitting}
          loading={isSubmitting}
          theme={{ colors: { primary: colors.primary } }}
        >
          Exit
        </FixedFontButton>
        <FixedFontButton
          mode="contained"
          onPress={handleSubmit}
          style={styles.submitButton}
          labelStyle={{ color: '#FFFFFF', fontWeight: '600' }}
          disabled={isSubmitting}
          loading={isSubmitting}
          theme={{ colors: { primary: colors.primary } }}
        >
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </FixedFontButton>
      </View>

      {/* Overlay to disable interactions during loading */}
      {isSubmitting && (
        <View style={styles.loadingOverlay} />
      )}

      <ProgressBar
        progress={(currentQuestion + 1) / questions.length}
        color={colors.primary}
        style={{ width: '100%', height: 4 }}
      />

      {/* Bottom Navigation */}
      <View style={[styles.bottomBar, { borderColor: colors.surfaceVariant }]}>
        <IconButton
          icon="chevron-left"
          disabled={currentQuestion === 0}
          onPress={handlePrevious}
        />

        <IconButton
          icon={isCurrentlyBookmarked ? 'bookmark' : 'bookmark-outline'}
          onPress={handleBookmarkToggle}
          iconColor={isCurrentlyBookmarked ? colors.primary : colors.onSurface}
        />

        <IconButton
          icon={() => {
            const isCurrentQuestionFlagged = flaggedIds.has(questions[currentQuestion]?.originalIndex);
            return (
              <MaterialCommunityIcons
                size={24}
                name={isCurrentQuestionFlagged ? "flag" : "flag-outline"}
                color={isCurrentQuestionFlagged ? colors.error : colors.onSurface}
              />
            );
          }}
          onPress={toggleFlag}
        />

        <IconButton
          icon="chevron-right"
          disabled={currentQuestion === questions.length - 1}
          onPress={handleNext}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  counter: {
    fontSize: 16,
    fontWeight: '500',
  },
  creditContainer: {
    position: 'relative',
    padding: 8,
  },
  badge: {
    position: 'absolute',
    top: -4,
    right: -8,
  },
  content: {
    paddingVertical: 24,
    paddingHorizontal: 29, // Increased from 24 to 29 (5px more horizontal margin)
  },
  question: {
    fontSize: 18,
    lineHeight: 24,
    marginBottom: 16,
  },
  questionType: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
    marginTop: 8,
  },
  questionPrompt: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 24,
  },
  choicesContainer: {
    gap: 12,
  },
  choiceButton: {
    borderRadius: 8,
    borderWidth: 1,
    padding: 16,
  },
  choiceText: {
    fontSize: 16,
    fontWeight: '500',
  },
  progress: {
    marginTop: 24,
    height: 6,
    borderRadius: 3,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  submitButton: {
    borderRadius: 8,
    width: '47%',
  },
  statusPanel: {
    zIndex: 1,
    borderBottomWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    overflow: 'hidden',
    paddingHorizontal: 12,  // Add padding to prevent edge crowding
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  panelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  headerIcon: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  panel: {
    overflow: 'hidden',
    elevation: 2,
    maxHeight: Dimensions.get('window').height * 0.35, // Limit maximum height
    margin: 8
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
    alignItems: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6, // Reduced from 8
    justifyContent: 'flex-start',
    minHeight: 100, // Reduced from 120
  },
  gridItem: {
    width: 40,  // Slightly larger for better touch targets
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    position: 'relative',
  },
  flagIcon: {
    position: 'absolute',
    top: 2,
    left: 2,
  },
  bookmarkIcon: {
    position: 'absolute',
    top: 2,
    right: 2,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 16,
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  paginationButton: {
    marginHorizontal: -8
  },
  // Image modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height / 2,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    padding: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 5,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    padding: 16,
    gap: 8,
  },
  gridButton: {
    width: 40,
    height: 40,
    margin: 4,
    borderRadius: 20,
    borderWidth: 1,
  },
  gridLabel: {
    fontSize: 12,
    margin: 0,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    zIndex: 1000,
    elevation: 5,
  },
  gridText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default QuizMode;